17:30:04.888 选择指派给开始 at pages/task/assign.uvue:565
17:30:04.889 当前selectedStationData: null at pages/task/assign.uvue:566
17:30:04.889 没有已选择的数据 at pages/task/assign.uvue:582
17:30:05.446 页面参数 options: ‍[⁠UTSJSONObject⁠]‍ { ⁠...⁠ } at pages/common/dept-post-user-select.uvue:92
17:30:05.446 selectedData参数: null at pages/common/dept-post-user-select.uvue:93
17:30:05.446 页面初始化开始 at pages/common/dept-post-user-select.uvue:275
17:30:05.447 从URL获取的selectedData参数: null at pages/common/dept-post-user-select.uvue:279
17:30:05.447 没有传入选择数据或数据为空 at pages/common/dept-post-user-select.uvue:313
17:30:05.447 页面初始化完成 at pages/common/dept-post-user-select.uvue:316
17:30:05.447 进入页面:​/pages/common/dept-post-user-select​ 。[{"创建dom元素个数":"15个","耗时":"7ms"},{"排版":"1次","耗时":"4ms"},{"渲染":"1次","耗时":"19ms"},{"跳转页面到onReady总耗时":"147ms"}]
17:30:06.348 === 复选框点击 === at pages/common/dept-post-user-select.uvue:481
17:30:06.349 点击的项目: 技术部 1 1001 at pages/common/dept-post-user-select.uvue:482
17:30:06.349 添加选择: 技术部 at pages/common/dept-post-user-select.uvue:491
17:30:06.354 当前selectedItems长度: ‍[number]‍ 1 at pages/common/dept-post-user-select.uvue:495
17:30:06.355 当前selectedItems: ‍[Array]‍ [ "技术部(1)" ] at pages/common/dept-post-user-select.uvue:496
17:30:06.355 === 开始更新组织架构数据 === at pages/common/dept-post-user-select.uvue:355
17:30:06.417 当前selectedItems: ‍[Array]‍ [ {id: "1001", isChild: "1", name: "技术部", parentId: 0, type: "1"} ] at pages/common/dept-post-user-select.uvue:356
17:30:06.417 第一步：处理选中的部门 at pages/common/dept-post-user-select.uvue:361
17:30:06.417 找到部门: 技术部 1001 at pages/common/dept-post-user-select.uvue:364
17:30:06.417 部门处理完成，orgBosMap大小: ‍[number]‍ 1 at pages/common/dept-post-user-select.uvue:373
17:30:06.418 第二步：处理岗位和用户 at pages/common/dept-post-user-select.uvue:376
17:30:06.418 处理第1个选择项: 技术部 1 1001 at pages/common/dept-post-user-select.uvue:378
17:30:06.418 第三步：生成最终数组 at pages/common/dept-post-user-select.uvue:459
17:30:06.418 orgBosMap最终大小: ‍[number]‍ 1 at pages/common/dept-post-user-select.uvue:460
17:30:06.443 添加orgBos到数组，key: 1001 value: ‍[⁠OrgBosItem⁠]‍ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/common/dept-post-user-select.uvue:464
17:30:06.444 === 组织架构数据更新完成 === at pages/common/dept-post-user-select.uvue:469
17:30:06.444 最终orgBosData: ‍[Array]‍ [ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} ] at pages/common/dept-post-user-select.uvue:470
17:30:06.444 orgBosData长度: ‍[number]‍ 1 at pages/common/dept-post-user-select.uvue:471
17:30:06.734 === 复选框点击 === at pages/common/dept-post-user-select.uvue:481
17:30:06.741 点击的项目: 市场部 1 1002 at pages/common/dept-post-user-select.uvue:482
17:30:06.742 添加选择: 市场部 at pages/common/dept-post-user-select.uvue:491
17:30:06.742 当前selectedItems长度: ‍[number]‍ 2 at pages/common/dept-post-user-select.uvue:495
17:30:06.742 当前selectedItems: ‍[Array]‍ [ "技术部(1)", "市场部(1)" ] at pages/common/dept-post-user-select.uvue:496
17:30:06.742 === 开始更新组织架构数据 === at pages/common/dept-post-user-select.uvue:355
17:30:06.762 当前selectedItems: ‍[Array]‍ [ {id: "1001", isChild: "1", name: "技术部", parentId: 0, type: "1"}, {id: "1002", isChild: "1", name: "市场部", parentId: 0, type: "1"} ] at pages/common/dept-post-user-select.uvue:356
17:30:06.762 第一步：处理选中的部门 at pages/common/dept-post-user-select.uvue:361
17:30:06.772 找到部门: 技术部 1001 at pages/common/dept-post-user-select.uvue:364
17:30:06.773 找到部门: 市场部 1002 at pages/common/dept-post-user-select.uvue:364
17:30:06.773 部门处理完成，orgBosMap大小: ‍[number]‍ 2 at pages/common/dept-post-user-select.uvue:373
17:30:06.773 第二步：处理岗位和用户 at pages/common/dept-post-user-select.uvue:376
17:30:06.773 处理第1个选择项: 技术部 1 1001 at pages/common/dept-post-user-select.uvue:378
17:30:06.773 处理第2个选择项: 市场部 1 1002 at pages/common/dept-post-user-select.uvue:378
17:30:06.773 第三步：生成最终数组 at pages/common/dept-post-user-select.uvue:459
17:30:06.773 orgBosMap最终大小: ‍[number]‍ 2 at pages/common/dept-post-user-select.uvue:460
17:30:06.797 添加orgBos到数组，key: 1001 value: ‍[⁠OrgBosItem⁠]‍ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/common/dept-post-user-select.uvue:464
17:30:06.798 添加orgBos到数组，key: 1002 value: ‍[⁠OrgBosItem⁠]‍ {deptId: "1002", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/common/dept-post-user-select.uvue:464
17:30:06.846 === 组织架构数据更新完成 === at pages/common/dept-post-user-select.uvue:469
17:30:06.846 最终orgBosData: ‍[Array]‍ [ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]}, {deptId: "1002", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} ] at pages/common/dept-post-user-select.uvue:470
17:30:06.846 orgBosData长度: ‍[number]‍ 2 at pages/common/dept-post-user-select.uvue:471
17:30:07.779 === 复选框点击 === at pages/common/dept-post-user-select.uvue:481
17:30:07.779 点击的项目: 人事部 1 1003 at pages/common/dept-post-user-select.uvue:482
17:30:07.779 添加选择: 人事部 at pages/common/dept-post-user-select.uvue:491
17:30:07.788 当前selectedItems长度: ‍[number]‍ 3 at pages/common/dept-post-user-select.uvue:495
17:30:07.789 当前selectedItems: ‍[Array]‍ [ "技术部(1)", "市场部(1)", "人事部(1)" ] at pages/common/dept-post-user-select.uvue:496
17:30:07.789 === 开始更新组织架构数据 === at pages/common/dept-post-user-select.uvue:355
17:30:07.806 当前selectedItems: ‍[Array]‍ [ {id: "1001", isChild: "1", name: "技术部", parentId: 0, type: "1"}, {id: "1002", isChild: "1", name: "市场部", parentId: 0, type: "1"}, {id: "1003", isChild: "0", name: "人事部", parentId: 0, type: "1"} ] at pages/common/dept-post-user-select.uvue:356
17:30:07.807 第一步：处理选中的部门 at pages/common/dept-post-user-select.uvue:361
17:30:07.807 找到部门: 技术部 1001 at pages/common/dept-post-user-select.uvue:364
17:30:07.807 找到部门: 市场部 1002 at pages/common/dept-post-user-select.uvue:364
17:30:07.807 找到部门: 人事部 1003 at pages/common/dept-post-user-select.uvue:364
17:30:07.807 部门处理完成，orgBosMap大小: ‍[number]‍ 3 at pages/common/dept-post-user-select.uvue:373
17:30:07.807 第二步：处理岗位和用户 at pages/common/dept-post-user-select.uvue:376
17:30:07.807 处理第1个选择项: 技术部 1 1001 at pages/common/dept-post-user-select.uvue:378
17:30:07.807 处理第2个选择项: 市场部 1 1002 at pages/common/dept-post-user-select.uvue:378
17:30:07.810 处理第3个选择项: 人事部 1 1003 at pages/common/dept-post-user-select.uvue:378
17:30:07.811 第三步：生成最终数组 at pages/common/dept-post-user-select.uvue:459
17:30:07.811 orgBosMap最终大小: ‍[number]‍ 3 at pages/common/dept-post-user-select.uvue:460
17:30:07.820 添加orgBos到数组，key: 1001 value: ‍[⁠OrgBosItem⁠]‍ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/common/dept-post-user-select.uvue:464
17:30:07.820 添加orgBos到数组，key: 1002 value: ‍[⁠OrgBosItem⁠]‍ {deptId: "1002", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/common/dept-post-user-select.uvue:464
17:30:07.858 添加orgBos到数组，key: 1003 value: ‍[⁠OrgBosItem⁠]‍ {deptId: "1003", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/common/dept-post-user-select.uvue:464
17:30:07.858 === 组织架构数据更新完成 === at pages/common/dept-post-user-select.uvue:469
17:30:07.858 最终orgBosData: ‍[Array]‍ [ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]}, {deptId: "1002", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]}, {deptId: "1003", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} ] at pages/common/dept-post-user-select.uvue:470
17:30:07.859 orgBosData长度: ‍[number]‍ 3 at pages/common/dept-post-user-select.uvue:471
17:30:09.071 接收到选择结果: ‍[⁠OrgBosResult⁠]‍ {orgBos: [ ⁠...⁠ ]} at pages/task/assign.uvue:592
17:30:09.072 result.orgBos长度: ‍[number]‍ 3 at pages/task/assign.uvue:593
17:30:09.081 处理orgBos: ‍[⁠OrgBosItem⁠]‍ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/task/assign.uvue:603
17:30:09.092 处理orgBos: ‍[⁠OrgBosItem⁠]‍ {deptId: "1002", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/task/assign.uvue:603
17:30:09.103 处理orgBos: ‍[⁠OrgBosItem⁠]‍ {deptId: "1003", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/task/assign.uvue:603
17:30:09.103 构建的显示文本: 已选择3个部门 at pages/task/assign.uvue:633
17:30:09.104 保存的完整数据: ‍[⁠Proxy(OrgBosResult)⁠]‍ {orgBos: [ ⁠...⁠ ]} at pages/task/assign.uvue:638
17:30:09.123 开始转换显示数据: ‍[⁠OrgBosResult⁠]‍ {orgBos: [ ⁠...⁠ ]} at pages/task/assign.uvue:365
17:30:09.135 处理第1个orgBos: ‍[⁠Proxy(OrgBosItem)⁠]‍ {deptId: "1001", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/task/assign.uvue:369
17:30:09.136 添加部门显示项: 技术部 at pages/task/assign.uvue:380
17:30:09.136 处理第2个orgBos: ‍[⁠Proxy(OrgBosItem)⁠]‍ {deptId: "1002", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/task/assign.uvue:369
17:30:09.137 添加部门显示项: 市场部 at pages/task/assign.uvue:380
17:30:09.182 处理第3个orgBos: ‍[⁠Proxy(OrgBosItem)⁠]‍ {deptId: "1003", postIds: ‍[Array]‍ [ ⁠...⁠ ], userIds: ‍[Array]‍ [ ⁠...⁠ ]} at pages/task/assign.uvue:369
17:30:09.182 添加部门显示项: 人事部 at pages/task/assign.uvue:380
17:30:09.182 转换完成，显示项总数: ‍[number]‍ 3 at pages/task/assign.uvue:408
17:30:09.182 转换后的显示数据: ‍[Array]‍ [ {id: "dept_1001", originalId: "1001", path: "技术部", type: "dept"}, {id: "dept_1002", originalId: "1002", path: "市场部", type: "dept"}, {id: "dept_1003", originalId: "1003", path: "人事部", type: "dept"} ] at pages/task/assign.uvue:642
17:30:09.183 检查存储数据:  at pages/task/assign.uvue:991
17:30:09.183 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:09.562 检查存储数据:  at pages/task/assign.uvue:991
17:30:09.562 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:10.088 检查存储数据:  at pages/task/assign.uvue:991
17:30:10.088 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:10.561 检查存储数据:  at pages/task/assign.uvue:991
17:30:10.561 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:11.061 检查存储数据:  at pages/task/assign.uvue:991
17:30:11.061 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:11.562 检查存储数据:  at pages/task/assign.uvue:991
17:30:11.562 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:12.064 检查存储数据:  at pages/task/assign.uvue:991
17:30:12.064 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:12.562 检查存储数据:  at pages/task/assign.uvue:991
17:30:12.562 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:13.071 检查存储数据:  at pages/task/assign.uvue:991
17:30:13.071 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:13.572 检查存储数据:  at pages/task/assign.uvue:991
17:30:13.572 没有找到存储的清单数据 at pages/task/assign.uvue:999
17:30:16.025 选择指派给开始 at pages/task/assign.uvue:565
17:30:16.052 当前selectedStationData: ‍[⁠Proxy(OrgBosResult)⁠]‍ {orgBos: [ ⁠...⁠ ]} at pages/task/assign.uvue:566
17:30:16.058 有已选择的数据，准备传递 at pages/task/assign.uvue:572
17:30:16.058 序列化后的数据: {"orgBos":[{"deptId":"1001","postIds":[],"userIds":[]},{"deptId":"1002","postIds":[],"userIds":[]},{"deptId":"1003","postIds":[],"userIds":[]}]} at pages/task/assign.uvue:574
17:30:16.058 编码后的数据: %7B%22orgBos%22%3A%5B%7B%22deptId%22%3A%221001%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%2C%7B%22deptId%22%3A%221002%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%2C%7B%22deptId%22%3A%221003%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%5D%7D at pages/task/assign.uvue:577
17:30:16.059 最终URL: /pages/common/dept-post-user-select?selectedData=%7B%22orgBos%22%3A%5B%7B%22deptId%22%3A%221001%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%2C%7B%22deptId%22%3A%221002%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%2C%7B%22deptId%22%3A%221003%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%5D%7D at pages/task/assign.uvue:580
17:30:16.068 页面参数 options: ‍[⁠UTSJSONObject⁠]‍ { ⁠...⁠ } at pages/common/dept-post-user-select.uvue:92
17:30:16.068 selectedData参数: null at pages/common/dept-post-user-select.uvue:93
17:30:16.068 页面初始化开始 at pages/common/dept-post-user-select.uvue:275
17:30:16.086 从URL获取的selectedData参数: null at pages/common/dept-post-user-select.uvue:279
17:30:16.086 没有传入选择数据或数据为空 at pages/common/dept-post-user-select.uvue:313
17:30:16.086 页面初始化完成 at pages/common/dept-post-user-select.uvue:316
17:30:16.190 进入页面:​/pages/common/dept-post-user-select?selectedData=%7B%22orgBos%22%3A%5B%7B%22deptId%22%3A%221001%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%2C%7B%22deptId%22%3A%221002%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%2C%7B%22deptId%22%3A%221003%22%2C%22postIds%22%3A%5B%5D%2C%22userIds%22%3A%5B%5D%7D%5D%7D​ 。[{"创建dom元素个数":"15个","耗时":"23ms"},{"排版":"1次","耗时":"3ms"},{"渲染":"1次","耗时":"16ms"},{"跳转页面到onReady总耗时":"156ms"}]
