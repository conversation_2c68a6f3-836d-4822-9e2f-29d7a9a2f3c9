<template>
		<view class="status_bar">
			<!-- 这里是状态栏 -->
		</view>
	<!-- #ifdef APP -->
	<scroll-view style="flex: 1; background-color: #f5f5f5;" show-scrollbar="false">
	<!-- #endif -->
	<!-- #ifndef APP -->
	<view class="web-scroll-container">
	<!-- #endif -->
		<view class="task-assign-container">
			<!-- 顶部导航栏 -->
			<view class="nav-bar">
				<view class="nav-left" @click="handleBack">
					<text class="back-icon">‹</text>
				</view>
				<view class="nav-center">
					<text class="nav-title">任务下发</text>
				</view>
				<view class="nav-right" @click="handleViewRecords">
					<text class="nav-link">下发记录</text>
				</view>
			</view>

			<!-- 表单区域 -->
			<view class="form-container">
				<!-- 选择清单 -->
				<view class="form-item">
					<text class="form-label">选择清单</text>
					<view class="form-value-container">
						<view class="select-trigger" @click="handleSelectChecklist">
							<text class="select-text" :class="{ 'placeholder': selectedChecklists.length === 0 }">
								{{ selectedChecklists.length === 0 ? '请选择清单' : `已选择 ${selectedChecklists.length} 个清单` }}
							</text>
							<text class="arrow-icon">›</text>
						</view>
					</view>
				</view>

				<!-- 已选择的清单列表 -->
				<view class="selected-checklists-container" v-if="selectedChecklists.length > 0">
					<text class="selected-checklists-title">已选择的清单</text>
					<view class="selected-checklists-list">
						<view v-for="(item, index) in selectedChecklists" :key="index" class="selected-checklist-item">
							<view class="checklist-info">
								<text class="checklist-name">{{ item.inventoryName }}</text>
								<text class="checklist-detail">{{ item.stepCateName }} - {{ item.stepName }} - {{ item.cateName }}</text>
							</view>
							<view class="remove-button" @click="removeChecklist(index)">
								<text class="iconfont icon_font">&#xe605;</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 预计开始时间 -->
				<view class="form-item">
					<text class="form-label">预计开始时间</text>
					<view class="form-value-container">
						<DateTimePicker
							mode="datetime"
							:value="startTimeValue"
							placeholder="请选择开始时间"
							@change="handleStartTimeChange"
						/>
					</view>
				</view>

				<!-- 预计结束时间 -->
				<view class="form-item">
					<text class="form-label">预计结束时间</text>
					<view class="form-value-container">
						<DateTimePicker
							mode="datetime"
							:value="endTimeValue"
							placeholder="请选择结束时间"
							@change="handleEndTimeChange"
						/>
					</view>
				</view>

				<!-- 指派给 -->
				<view class="form-item">
					<text class="form-label">指派给</text>
					<view class="form-value-container">
						<view class="select-trigger" @click="handleSelectStation">
							<text class="select-text" :class="{ 'placeholder': selectedStation === '' }">
								{{ selectedStation === '' ? '请选择指派' : selectedStation }}
							</text>
							<text class="arrow-icon">›</text>
						</view>
					</view>
				</view>

				<!-- 选择结果展示 -->
				<view class="selection-display" v-if="displayItems.length > 0">
					<view class="display-header">
						<text class="header-title">已选择的指派对象 ({{ displayItems.length }})</text>
					</view>

					<view class="hierarchical-list">
						<view class="dept-group" v-for="deptGroup in hierarchicalDisplayData" :key="deptGroup.deptId">
							<!-- 部门标题 -->
							<view class="dept-item" v-if="deptGroup.isDeptSelected">
								<text class="dept-name">{{ deptGroup.deptName }}</text>
								<text class="delete-btn" @click="handleDeleteDept(deptGroup.deptId)">×</text>
							</view>
							<view class="dept-title" v-else>
								<text class="dept-name">{{ deptGroup.deptName }}</text>
							</view>

							<!-- 岗位列表 -->
							<view class="post-list" v-if="deptGroup.posts.length > 0">
								<view class="post-item" v-for="post in deptGroup.posts" :key="post.postId">
									<text class="post-name">{{ post.postName }}</text>
									<text class="delete-btn" v-if="post.isPostSelected" @click="handleDeletePost(post.postId)">×</text>
								</view>
							</view>

							<!-- 用户列表 -->
							<view class="user-list" v-if="deptGroup.users.length > 0">
								<view class="user-item" v-for="user in deptGroup.users" :key="user.userId">
									<text class="user-name">{{ user.userName }}</text>
									<text class="delete-btn" @click="handleDeleteUser(user.userId)">×</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态展示 -->
				<view class="empty-state" v-else>
					<text class="empty-text">暂无选择任何人员</text>
					<text class="empty-hint">请点击"请选择指派"进行选择</text>
				</view>
			</view>

			<!-- 底部按钮 -->
			<view class="bottom-container">
				<view class="confirm-button" @click="handleConfirmAssign">
					<text class="confirm-text">确认下发</text>
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
	<!-- #ifndef APP -->
	</view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, computed, onMounted, onUnmounted } from 'vue'
	import DateTimePicker from '@/components/DateTimePicker.uvue'
	import type { InventoryItem, DeptPostUserSelectResult, OrgBosResult, OrgBosItem, DisplayItem } from '@/utils/apiType.uts'

	// 层次化展示数据类型
	type HierarchicalPost = {
		postId: string;
		postName: string;
		isPostSelected: boolean; // 是否选择了整个岗位
	}

	type HierarchicalUser = {
		userId: string;
		userName: string;
	}

	type HierarchicalDept = {
		deptId: string;
		deptName: string;
		isDeptSelected: boolean; // 是否选择了整个部门
		posts: Array<HierarchicalPost>;
		users: Array<HierarchicalUser>;
	}

	// 表单数据
	const selectedChecklists = ref<Array<InventoryItem>>([]) // 改为数组存储多个清单
	const startTimeValue = ref<Date | null>(null)
	const endTimeValue = ref<Date | null>(null)
	const selectedStation = ref<string>('')
	const selectedStationData = ref<OrgBosResult | null>(null) // 保存完整的选择数据
	const displayItems = ref<Array<DisplayItem>>([]) // 展示用的数据

	// 模拟数据映射（与选择页面保持一致）
	const mockDataMap = new Map<string, any>()

	// 初始化模拟数据
	const initMockData = function(): void {
		// 部门数据
		const dept1001 = new UTSJSONObject()
		dept1001.set('name', '技术部')
		dept1001.set('type', 'dept')
		dept1001.set('parentId', '')
		mockDataMap.set('1001', dept1001)

		const dept1002 = new UTSJSONObject()
		dept1002.set('name', '市场部')
		dept1002.set('type', 'dept')
		dept1002.set('parentId', '')
		mockDataMap.set('1002', dept1002)

		const dept1003 = new UTSJSONObject()
		dept1003.set('name', '人事部')
		dept1003.set('type', 'dept')
		dept1003.set('parentId', '')
		mockDataMap.set('1003', dept1003)

		const dept1004 = new UTSJSONObject()
		dept1004.set('name', '财务部')
		dept1004.set('type', 'dept')
		dept1004.set('parentId', '')
		mockDataMap.set('1004', dept1004)

		// 岗位数据
		const post2001 = new UTSJSONObject()
		post2001.set('name', '前端开发')
		post2001.set('type', 'post')
		post2001.set('parentId', '1001')
		mockDataMap.set('2001', post2001)

		const post2002 = new UTSJSONObject()
		post2002.set('name', '后端开发')
		post2002.set('type', 'post')
		post2002.set('parentId', '1001')
		mockDataMap.set('2002', post2002)

		const post2003 = new UTSJSONObject()
		post2003.set('name', '测试工程师')
		post2003.set('type', 'post')
		post2003.set('parentId', '1001')
		mockDataMap.set('2003', post2003)

		const post2004 = new UTSJSONObject()
		post2004.set('name', '市场专员')
		post2004.set('type', 'post')
		post2004.set('parentId', '1002')
		mockDataMap.set('2004', post2004)

		const post2005 = new UTSJSONObject()
		post2005.set('name', '销售经理')
		post2005.set('type', 'post')
		post2005.set('parentId', '1002')
		mockDataMap.set('2005', post2005)

		// 用户数据
		const user3001 = new UTSJSONObject()
		user3001.set('name', '张三')
		user3001.set('type', 'user')
		user3001.set('parentId', '2001')
		mockDataMap.set('3001', user3001)

		const user3002 = new UTSJSONObject()
		user3002.set('name', '李四')
		user3002.set('type', 'user')
		user3002.set('parentId', '2001')
		mockDataMap.set('3002', user3002)

		const user3003 = new UTSJSONObject()
		user3003.set('name', '王五')
		user3003.set('type', 'user')
		user3003.set('parentId', '2001')
		mockDataMap.set('3003', user3003)

		const user3004 = new UTSJSONObject()
		user3004.set('name', '赵六')
		user3004.set('type', 'user')
		user3004.set('parentId', '2002')
		mockDataMap.set('3004', user3004)

		const user3005 = new UTSJSONObject()
		user3005.set('name', '钱七')
		user3005.set('type', 'user')
		user3005.set('parentId', '2002')
		mockDataMap.set('3005', user3005)

		const user3006 = new UTSJSONObject()
		user3006.set('name', '孙八')
		user3006.set('type', 'user')
		user3006.set('parentId', '2003')
		mockDataMap.set('3006', user3006)

		const user3007 = new UTSJSONObject()
		user3007.set('name', '周九')
		user3007.set('type', 'user')
		user3007.set('parentId', '2004')
		mockDataMap.set('3007', user3007)

		const user3008 = new UTSJSONObject()
		user3008.set('name', '吴十')
		user3008.set('type', 'user')
		user3008.set('parentId', '2004')
		mockDataMap.set('3008', user3008)

		const user3009 = new UTSJSONObject()
		user3009.set('name', '郑十一')
		user3009.set('type', 'user')
		user3009.set('parentId', '2005')
		mockDataMap.set('3009', user3009)
	}

	// 根据ID查找名称
	const findNameById = function(id: string): string {
		const item = mockDataMap.get(id)
		if (item != null) {
			const itemObj = item as UTSJSONObject
			return itemObj.getString('name') ?? `未知(${id})`
		}
		return `未知(${id})`
	}

	// 构建用户的完整路径
	const buildUserPath = function(userId: string, deptId: string): string {
		const user = mockDataMap.get(userId)
		if (user == null) return `未知用户(${userId})`

		const userObj = user as UTSJSONObject
		const postId = userObj.getString('parentId') ?? ''
		const post = mockDataMap.get(postId)
		if (post == null) return userObj.getString('name') ?? `未知用户(${userId})`

		const postObj = post as UTSJSONObject
		const userName = userObj.getString('name') ?? `未知用户(${userId})`
		const postName = postObj.getString('name') ?? `未知岗位(${postId})`

		// 如果有部门ID且不为空，使用部门名称
		if (deptId !== '') {
			const dept = mockDataMap.get(deptId)
			const deptName = dept != null ? (dept as UTSJSONObject).getString('name') ?? `未知部门(${deptId})` : `未知部门(${deptId})`
			return `${deptName} > ${postName} > ${userName}`
		} else {
			// 没有部门ID，尝试从岗位的parentId获取部门
			const actualDeptId = postObj.getString('parentId') ?? ''
			const dept = mockDataMap.get(actualDeptId)
			const deptName = dept != null ? (dept as UTSJSONObject).getString('name') ?? '' : ''
			return deptName !== '' ? `${deptName} > ${postName} > ${userName}` : `${postName} > ${userName}`
		}
	}

	// 构建岗位的完整路径
	const buildPostPath = function(postId: string, deptId: string): string {
		const post = mockDataMap.get(postId)
		if (post == null) return `未知岗位(${postId})`

		const postObj = post as UTSJSONObject
		const postName = postObj.getString('name') ?? `未知岗位(${postId})`

		// 如果有部门ID且不为空，使用部门名称
		if (deptId !== '') {
			const dept = mockDataMap.get(deptId)
			const deptName = dept != null ? (dept as UTSJSONObject).getString('name') ?? `未知部门(${deptId})` : `未知部门(${deptId})`
			return `${deptName} > ${postName}`
		} else {
			// 没有部门ID，尝试从岗位的parentId获取部门
			const actualDeptId = postObj.getString('parentId') ?? ''
			const dept = mockDataMap.get(actualDeptId)
			const deptName = dept != null ? (dept as UTSJSONObject).getString('name') ?? '' : ''
			return deptName !== '' ? `${deptName} > ${postName}` : postName
		}
	}

	// 将OrgBosResult转换为DisplayItem数组
	const convertOrgBosToDisplayItems = function(orgBosResult: OrgBosResult): Array<DisplayItem> {
		console.log('开始转换显示数据:', orgBosResult)
		const displayItems: Array<DisplayItem> = []

		orgBosResult.orgBos.forEach((orgBos, index) => {
			console.log(`处理第${index + 1}个orgBos:`, orgBos)

			// 处理部门
			if (orgBos.deptId !== '') {
				const deptName = findNameById(orgBos.deptId)
				displayItems.push({
					id: `dept_${orgBos.deptId}`,
					path: deptName,
					type: 'dept',
					originalId: orgBos.deptId
				})
				console.log('添加部门显示项:', deptName)
			}

			// 处理岗位
			orgBos.postIds.forEach(postId => {
				const path = buildPostPath(postId, orgBos.deptId)
				displayItems.push({
					id: `post_${postId}`,
					path: path,
					type: 'post',
					originalId: postId
				})
				console.log('添加岗位显示项:', path)
			})

			// 处理用户
			orgBos.userIds.forEach(userId => {
				const path = buildUserPath(userId, orgBos.deptId)
				displayItems.push({
					id: `user_${userId}`,
					path: path,
					type: 'user',
					originalId: userId
				})
				console.log('添加用户显示项:', path)
			})
		})

		console.log('转换完成，显示项总数:', displayItems.length)
		return displayItems
	}

	// 层次化展示数据计算属性
	const hierarchicalDisplayData = computed(function(): Array<HierarchicalDept> {
		const result: Array<HierarchicalDept> = []
		const deptMap = new Map<string, HierarchicalDept>()

		// 遍历所有显示项，按部门分组
		displayItems.value.forEach(item => {
			let deptId = ''
			let deptName = ''

			if (item.type === 'dept') {
				deptId = item.originalId
				deptName = findNameById(deptId)
			} else if (item.type === 'post') {
				// 从岗位找到所属部门
				const post = mockDataMap.get(item.originalId)
				deptId = post != null ? (post as UTSJSONObject).getString('parentId') ?? '' : ''
				deptName = findNameById(deptId)
			} else if (item.type === 'user') {
				// 从用户找到所属部门
				const user = mockDataMap.get(item.originalId)
				if (user != null) {
					const postId = (user as UTSJSONObject).getString('parentId') ?? ''
					const post = mockDataMap.get(postId)
					deptId = post != null ? (post as UTSJSONObject).getString('parentId') ?? '' : ''
					deptName = findNameById(deptId)
				}
			}

			// 确保部门存在于映射中
			if (!deptMap.has(deptId)) {
				deptMap.set(deptId, {
					deptId: deptId,
					deptName: deptName,
					isDeptSelected: false,
					posts: [] as Array<HierarchicalPost>,
					users: [] as Array<HierarchicalUser>
				})
			}

			const dept = deptMap.get(deptId)
			if (dept != null) {
				if (item.type === 'dept') {
					dept.isDeptSelected = true
				} else if (item.type === 'post') {
					const postName = findNameById(item.originalId)
					dept.posts.push({
						postId: item.originalId,
						postName: postName,
						isPostSelected: true
					})
				} else if (item.type === 'user') {
					const userName = findNameById(item.originalId)
					dept.users.push({
						userId: item.originalId,
						userName: userName
					})
				}
			}
		})

		// 转换为数组
		deptMap.forEach((dept, key) => {
			result.push(dept)
		})

		return result
	})

	// 返回上一页
	const handleBack = (): void => {
		console.log('返回上一页')
		uni.navigateBack()
	}

	// 查看下发记录
	const handleViewRecords = (): void => {
		console.log('查看下发记录')
		uni.navigateTo({
			url: '/pages/task/records'
		})
	}

	// 选择清单
	const handleSelectChecklist = (): void => {
		console.log('选择清单')
		uni.navigateTo({
			url: '/pages/task/checklist-select'
		})
	}

	// 清单选择回调（处理多个清单数据）
	const onChecklistsSelected = (checklistsData: UTSJSONObject | null): void => {
		console.log('选择的清单数据:', checklistsData)
		if (checklistsData != null) {
			const selectedItems = checklistsData['selectedItems'] as Array<UTSJSONObject>
			if (selectedItems != null) {
				// 将 UTSJSONObject 转换为 InventoryItem 类型
				const convertedItems: Array<InventoryItem> = []
				selectedItems.forEach(function(jsonItem: UTSJSONObject) {
					const inventoryId = jsonItem['inventoryId']
					const inventoryName = jsonItem['inventoryName']
					const cateId = jsonItem['cateId']
					const cateName = jsonItem['cateName']
					const stepId = jsonItem['stepId']
					const stepName = jsonItem['stepName']
					const stepCateId = jsonItem['stepCateId']
					const stepCateName = jsonItem['stepCateName']

					if (inventoryId != null && inventoryName != null) {
						const item: InventoryItem = {
							inventoryId: inventoryId.toString(),
							inventoryName: inventoryName.toString(),
							cateId: cateId != null ? cateId.toString() : '',
							cateName: cateName != null ? cateName.toString() : '',
							stepId: stepId != null ? stepId.toString() : '',
							stepName: stepName != null ? stepName.toString() : '',
							stepCateId: stepCateId != null ? stepCateId.toString() : '',
							stepCateName: stepCateName != null ? stepCateName.toString() : ''
						}
						convertedItems.push(item)
						console.log('转换清单项:', item.inventoryName)
					}
				})
				selectedChecklists.value = convertedItems
				console.log('已更新选中清单列表，数量:', selectedChecklists.value.length)
			}
		}
	}

	// 移除单个清单
	const removeChecklist = (index: number): void => {
		console.log('移除清单，索引:', index)
		selectedChecklists.value.splice(index, 1)
		console.log('移除后清单数量:', selectedChecklists.value.length)
	}



	// 开始时间变化处理
	const handleStartTimeChange = (value: Date): void => {
		console.log('开始时间变化:', value)
		startTimeValue.value = value
	}

	// 结束时间变化处理
	const handleEndTimeChange = (value: Date): void => {
		console.log('结束时间变化:', value)
		endTimeValue.value = value
	}

	// 选择指派给
	const handleSelectStation = function(): void {
		console.log('选择指派给开始')
		console.log('当前selectedStationData:', selectedStationData.value)

		let url = '/pages/common/dept-post-user-select'

		// 如果有已选择的数据，传递给选择页面
		if (selectedStationData.value != null) {
			console.log('有已选择的数据，准备传递')
			const selectedDataStr = JSON.stringify(selectedStationData.value)
			console.log('序列化后的数据:', selectedDataStr)

			const encodedData = encodeURIComponent(selectedDataStr)
			console.log('编码后的数据:', encodedData)

			url += `?selectedData=${encodedData}`
			console.log('最终URL:', url)
		} else {
			console.log('没有已选择的数据')
		}

		uni.navigateTo({
			url: url
		})
	}

	// 部门岗位用户选择回调
	const onDeptPostUserSelected = function(result: OrgBosResult): void {
		console.log('接收到选择结果:', result)
		console.log('result.orgBos长度:', result.orgBos.length)

		// 构建显示文本
		let displayText = ''
		if (result.orgBos.length > 0) {
			let totalDepts = 0
			let totalPosts = 0
			let totalUsers = 0

			result.orgBos.forEach(orgBos => {
				console.log('处理orgBos:', orgBos)

				// 统计部门数量（只有deptId不为空才算部门）
				if (orgBos.deptId !== '') {
					totalDepts++
				}

				// 统计岗位和用户数量
				totalPosts += orgBos.postIds.length
				totalUsers += orgBos.userIds.length
			})

			const parts: string[] = []
			if (totalDepts > 0) {
				parts.push(`${totalDepts}个部门`)
			}
			if (totalPosts > 0) {
				parts.push(`${totalPosts}个岗位`)
			}
			if (totalUsers > 0) {
				parts.push(`${totalUsers}个用户`)
			}

			if (parts.length > 0) {
				displayText = `已选择${parts.join('、')}`
			} else {
				displayText = '未选择任何项目'
			}
		}

		console.log('构建的显示文本:', displayText)
		selectedStation.value = displayText

		// 保存完整的选择信息
		selectedStationData.value = result
		console.log('保存的完整数据:', selectedStationData.value)

		// 转换为显示数据
		displayItems.value = convertOrgBosToDisplayItems(result)
		console.log('转换后的显示数据:', displayItems.value)

		uni.showToast({
			title: displayText,
			icon: 'success'
		})
	}

	// 更新显示文本
	const updateDisplayText = function(): void {
		if (displayItems.value.length === 0) {
			selectedStation.value = ''
			return
		}

		let totalDepts = 0
		let totalPosts = 0
		let totalUsers = 0

		displayItems.value.forEach(item => {
			if (item.type === 'dept') totalDepts++
			else if (item.type === 'post') totalPosts++
			else if (item.type === 'user') totalUsers++
		})

		const parts: Array<string> = []
		if (totalDepts > 0) parts.push(`${totalDepts}个部门`)
		if (totalPosts > 0) parts.push(`${totalPosts}个岗位`)
		if (totalUsers > 0) parts.push(`${totalUsers}个用户`)

		selectedStation.value = parts.length > 0 ? `已选择${parts.join('、')}` : ''
	}

	// 根据displayItems重新构建selectedStationData
	const rebuildSelectedStationData = function(): void {
		console.log('重新构建选择数据')

		if (displayItems.value.length === 0) {
			selectedStationData.value = null
			selectedStation.value = ''
			return
		}

		// 按部门分组重新构建orgBos
		const deptMap = new Map<string, OrgBosItem>()

		displayItems.value.forEach(item => {
			if (item.type === 'dept') {
				// 部门
				if (!deptMap.has(item.originalId)) {
					const newOrgBos: OrgBosItem = {
						deptId: item.originalId,
						postIds: [] as Array<string>,
						userIds: [] as Array<string>
					}
					deptMap.set(item.originalId, newOrgBos)
				}
			} else if (item.type === 'post') {
				// 岗位 - 需要找到对应的部门
				const post = mockDataMap.get(item.originalId)
				const deptId = post != null ? (post as UTSJSONObject).getString('parentId') ?? '' : ''

				if (!deptMap.has(deptId)) {
					const newOrgBos: OrgBosItem = {
						deptId: '',  // 空字符串表示没有选择部门
						postIds: [] as Array<string>,
						userIds: [] as Array<string>
					}
					deptMap.set(deptId, newOrgBos)
				}
				const orgBos = deptMap.get(deptId)
				if (orgBos != null) {
					orgBos.postIds.push(item.originalId)
				}
			} else if (item.type === 'user') {
				// 用户 - 需要找到对应的部门
				const user = mockDataMap.get(item.originalId)
				if (user != null) {
					const userParentId = (user as UTSJSONObject).getString('parentId') ?? ''
					const post = mockDataMap.get(userParentId)
					const deptId = post != null ? (post as UTSJSONObject).getString('parentId') ?? '' : ''

					if (!deptMap.has(deptId)) {
						const newOrgBos: OrgBosItem = {
							deptId: '',  // 空字符串表示没有选择部门
							postIds: [] as Array<string>,
							userIds: [] as Array<string>
						}
						deptMap.set(deptId, newOrgBos)
					}
					const orgBos = deptMap.get(deptId)
					if (orgBos != null) {
						orgBos.userIds.push(item.originalId)
					}
				}
			}
		})

		const orgBosArray: Array<OrgBosItem> = []
		deptMap.forEach((value, key) => {
			orgBosArray.push(value)
		})

		const result: OrgBosResult = {
			orgBos: orgBosArray
		}
		selectedStationData.value = result

		// 更新显示文本
		updateDisplayText()

		console.log('重新构建完成:', selectedStationData.value)
	}

	// 删除单个选择项
	const handleDeleteItem = function(item: DisplayItem): void {
		console.log('删除选择项:', item)

		uni.showModal({
			title: '确认删除',
			content: `确定要删除"${item.path}"吗？`,
			success: (res) => {
				if (res.confirm) {
					// 从displayItems中删除
					const index = displayItems.value.findIndex(displayItem => displayItem.id === item.id)
					if (index >= 0) {
						displayItems.value.splice(index, 1)
					}

					// 重新构建selectedStationData
					rebuildSelectedStationData()

					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				}
			}
		})
	}

	// 删除部门
	const handleDeleteDept = function(deptId: string): void {
		console.log('删除部门:', deptId)
		const deptName = findNameById(deptId)

		uni.showModal({
			title: '确认删除',
			content: `确定要删除部门"${deptName}"吗？`,
			success: (res) => {
				if (res.confirm) {
					// 删除该部门的所有相关项
					displayItems.value = displayItems.value.filter(item => {
						if (item.type === 'dept' && item.originalId === deptId) {
							return false
						}
						// 删除该部门下的所有岗位和用户
						if (item.type === 'post') {
							const post = mockDataMap.get(item.originalId)
							const postDeptId = post != null ? (post as UTSJSONObject).getString('parentId') ?? '' : ''
							return postDeptId !== deptId
						}
						if (item.type === 'user') {
							const user = mockDataMap.get(item.originalId)
							if (user != null) {
								const postId = (user as UTSJSONObject).getString('parentId') ?? ''
								const post = mockDataMap.get(postId)
								const userDeptId = post != null ? (post as UTSJSONObject).getString('parentId') ?? '' : ''
								return userDeptId !== deptId
							}
						}
						return true
					})

					rebuildSelectedStationData()
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				}
			}
		})
	}

	// 删除岗位
	const handleDeletePost = function(postId: string): void {
		console.log('删除岗位:', postId)
		const postName = findNameById(postId)

		uni.showModal({
			title: '确认删除',
			content: `确定要删除岗位"${postName}"吗？`,
			success: (res) => {
				if (res.confirm) {
					// 删除该岗位及其下的所有用户
					displayItems.value = displayItems.value.filter(item => {
						if (item.type === 'post' && item.originalId === postId) {
							return false
						}
						// 删除该岗位下的所有用户
						if (item.type === 'user') {
							const user = mockDataMap.get(item.originalId)
							const userPostId = user != null ? (user as UTSJSONObject).getString('parentId') ?? '' : ''
							return userPostId !== postId
						}
						return true
					})

					rebuildSelectedStationData()
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				}
			}
		})
	}

	// 删除用户
	const handleDeleteUser = function(userId: string): void {
		console.log('删除用户:', userId)
		const userName = findNameById(userId)

		uni.showModal({
			title: '确认删除',
			content: `确定要删除用户"${userName}"吗？`,
			success: (res) => {
				if (res.confirm) {
					// 删除该用户
					displayItems.value = displayItems.value.filter(item => {
						return !(item.type === 'user' && item.originalId === userId)
					})

					rebuildSelectedStationData()
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				}
			}
		})
	}

	// 清空所有选择
	const handleClearAll = function(): void {
		console.log('清空所有选择')

		uni.showModal({
			title: '确认清空',
			content: '确定要清空所有选择吗？',
			success: (res) => {
				if (res.confirm) {
					displayItems.value = []
					selectedStationData.value = null
					selectedStation.value = ''

					uni.showToast({
						title: '已清空所有选择',
						icon: 'success'
					})
				}
			}
		})
	}





	// 监听选择事件
	onMounted(() => {
		initMockData() // 初始化模拟数据
		uni.$on('deptPostUserSelected', onDeptPostUserSelected)
	})

	onUnmounted(() => {
		uni.$off('deptPostUserSelected', onDeptPostUserSelected)
	})

	// 确认下发任务处理
	const handleConfirmAssign = (): void => {
		console.log('确认下发任务')

		// 表单验证
		if (selectedChecklists.value.length === 0) {
			uni.showToast({
				title: '请选择清单',
				icon: 'none'
			})
			return
		}

		if (startTimeValue.value === null) {
			uni.showToast({
				title: '请选择开始时间',
				icon: 'none'
			})
			return
		}

		if (endTimeValue.value === null) {
			uni.showToast({
				title: '请选择结束时间',
				icon: 'none'
			})
			return
		}

		// 验证结束时间必须晚于开始时间
		if (endTimeValue.value <= startTimeValue.value) {
			uni.showToast({
				title: '结束时间必须晚于开始时间',
				icon: 'none'
			})
			return
		}

		if (selectedStation.value === '') {
			uni.showToast({
				title: '请选择播报站',
				icon: 'none'
			})
			return
		}

		// TODO: 调用任务下发接口
		uni.showLoading({
			title: '下发中...'
		})

		// 模拟接口调用
		setTimeout(() => {
			uni.hideLoading()
			uni.showToast({
				title: '任务下发成功',
				icon: 'success'
			})

			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}, 2000)
	}

	// 检查选择的清单数据
	const checkSelectedChecklists = (): void => {
		try {
			const selectedData = uni.getStorageSync('selectedChecklistsData')
			console.log('检查存储数据:', selectedData)
			if (selectedData != null && selectedData != '') {
				const checklistsData = JSON.parse(selectedData as string) as UTSJSONObject
				console.log('解析后的数据:', checklistsData)
				onChecklistsSelected(checklistsData)
				// 清除存储数据，避免重复处理
				uni.removeStorageSync('selectedChecklistsData')
			} else {
				console.log('没有找到存储的清单数据')
			}
		} catch (error) {
			console.error('获取选择的清单数据失败:', error)
		}
	}

	// 页面生命周期
	onLoad(() => {
		console.log('任务下发页面加载')
	})
	// 页面显示时的处理逻辑（uni-app x 暂不支持 onShow）
	onShow(()=>{
		// 页面挂载时检查是否有选择的清单数据
		checkSelectedChecklists()
	
		// 设置定时器定期检查（用于处理页面返回的情况）
		const checkInterval = setInterval(() => {
			checkSelectedChecklists()
		}, 500)
	
		// 5秒后清除定时器
		setTimeout(() => {
			clearInterval(checkInterval)
		}, 5000)
	})
	onMounted(() => {

	})
</script>
<style scoped>

	.task-assign-container {
		background-color: #f5f5f5;

	}

	/* Web端滚动容器 */
	.web-scroll-container {
		background-color: #f5f5f5;
	}

	/* 顶部导航栏 */
	.nav-bar {
		background-color: #FFFFFF;
		height: 88rpx;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.nav-left {
		width: 80rpx;
		height: 88rpx;
		align-items: center;
		justify-content: center;
	}

	.back-icon {
		font-size: 36rpx;
		color: #333333;
		font-weight: bold;
	}

	.nav-center {
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.nav-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}

	.nav-right {
		width: 120rpx;
		height: 88rpx;
		align-items: center;
		justify-content: center;
	}

	.nav-link {
		font-size: 28rpx;
		color: #1976D2;
	}

	/* 表单容器 */
	.form-container {
		background-color: #FFFFFF;
		margin: 20rpx 30rpx;
		border-radius: 16rpx;
		padding: 0;
		overflow: hidden;
	}

	.form-item {
		flex-direction: row;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.form-item:active {
		background-color: #f8f8f8;
	}

	.form-label {
		font-size: 30rpx;
		color: #333333;
	}

	.form-value-container {
	
	}

	.select-trigger {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		background-color: #FFFFFF;
		min-height: 80rpx;
	}

	.select-trigger:active {
		background-color: #f8f8f8;
	}

	.select-text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}

	.select-text.placeholder {
		color: #999999;
	}

	.arrow-icon {
		font-size: 32rpx;
		color: #cccccc;
		font-weight: bold;
	}

	.calendar-icon {
		font-size: 28rpx;
		color: #cccccc;
		margin-left: 20rpx;
	}

	/* 底部容器 */
	.bottom-container {
		flex: 1;
		justify-content: flex-end;
		padding: 40rpx 30rpx;
		padding-bottom: 60rpx;
	}

	.confirm-button {
		background-color: #1976D2;
		height: 88rpx;
		border-radius: 44rpx;
		align-items: center;
		justify-content: center;
	}

	.confirm-button:active {
		background-color: #1565C0;
	}

	.confirm-text {
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	/* 已选择清单列表样式 */
	.selected-checklists-container {
		margin-top: 20rpx;
		background-color: #FFFFFF;
		border-radius: 12rpx;
		padding: 30rpx;
	}

	.selected-checklists-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.selected-checklists-list {
		flex-direction: column;
	}

	.selected-checklist-item {
		flex-direction: row;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.selected-checklist-item:last-child {
		border-bottom: none;
	}

	.checklist-info {
		flex: 1;
		flex-direction: column;
	}

	.checklist-name {
		font-size: 30rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.checklist-detail {
		font-size: 24rpx;
		color: #666666;
		line-height: 1.4;
	}

	.remove-button {
		width: 40rpx;
		height: 40rpx;
		border-radius: 30rpx;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
	}
	.qingdan-button{
		width: 40rpx;
		height: 40rpx;
		border-radius: 30rpx;
		align-items: center;
		justify-content: center;
	}
	.remove-icon {
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	/* 选择结果展示样式 */
	.selection-display {
		margin-top: 32rpx;
		background-color: #F9F9F9;
		border-radius: 16rpx;
		padding: 32rpx;
	}

	.display-header {
		margin-bottom: 24rpx;
	}

	.header-title {
		font-size: 32rpx;
		font-weight: 700;
		color: #333333;
	}

	.display-list {
		margin-bottom: 24rpx;
	}

	.display-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		    flex-direction: row;
	}

	.item-path {
		flex: 1;
		font-size: 30rpx;
		color: #333333;
		line-height: 1.4;
	}

	.delete-btn {
		font-size: 36rpx;
		color: #ff4d4f;
		padding: 8rpx;
		margin-left: 16rpx;
		font-weight: 700;
	}

	/* 层次化展示样式 */
	.hierarchical-list {
		flex-direction: column;
	}

	.dept-group {
		margin-bottom: 24rpx;
	}

	.dept-item {
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 2rpx solid #e0e0e0;
	}

	.dept-title {
		padding: 20rpx 0;
		border-bottom: 2rpx solid #e0e0e0;
	}

	.dept-name {
		font-size: 32rpx;
		font-weight: 700;
		color: #333333;
	}

	.post-list {
		flex-direction: column;
		padding-left: 40rpx;
	}

	.post-item {
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.post-name {
		font-size: 30rpx;
		color: #555555;
		font-weight: 400;
	}

	.user-list {
		flex-direction: column;
		padding-left: 80rpx;
	}

	.user-item {
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 12rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.user-name {
		font-size: 28rpx;
		color: #666666;
	}

	/* 空状态样式 */
	.empty-state {
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 40rpx;
		margin-top: 32rpx;
		background-color: #F9F9F9;
		border-radius: 16rpx;
	}

	.empty-text {
		font-size: 32rpx;
		color: #999999;
		margin-bottom: 16rpx;
	}

	.empty-hint {
		font-size: 28rpx;
		color: #cccccc;
	}

	.display-actions {
		flex-direction: row;
		justify-content: space-between;
	}

	.action-btn {
		flex: 1;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
		border: 2rpx solid #d9d9d9;
		background-color: #ffffff;
	}

	.action-btn.secondary {
		border-color: #1677ff;
		background-color: #f0f9ff;
	}

	.action-btn.secondary .btn-text {
		color: #1677ff;
	}

	.action-btn.danger {
		border-color: #ff4d4f;
		background-color: #fff2f0;
	}

	.action-btn.danger .btn-text {
		color: #ff4d4f;
	}

	.btn-text {
		font-size: 28rpx;
		font-weight: bold;
	}
</style>