import { Post, Get, Delete, Put, Gets, PostUpload } from "./request.uts";

import {LoginData} from '@/utils/apiType.uts'
type RequestControl = (task: any) => void;

	// 根据部门返回可选的岗位
export const login=(data: LoginData)=> {
	return Post("/app/frontAuth/login",data)
}

	// 根据部门返回可选的岗位
export const getDeptTree=()=> {
	return Get("/app/front/common/deptTree", {})
}

// 获取工序分类列表
export const getStepCategoryList = () => {
	return Get("/app/front/inventoryTask/stepCateList", {})
}

// 获取工序列表（根据工序分类ID筛选）
export function getStepList(stepCateId: string) {
	const url = `/app/front/inventoryTask/stepList?params[cateId]=${stepCateId}`
	return Get(url, {})
}

// 获取清单分类列表
export const getInventoryCategoryList = () => {
	return Get("/app/front/inventoryTask/inventoryCateList", {})
}

// 获取清单列表
export function getInventoryList(inventoryName?: string | null, cateId?: string | null, stepId?: string | null, stepCateId?: string | null) {
	const queryParts: string[] = []
	if (inventoryName != null && inventoryName != '') queryParts.push(`inventoryName=${encodeURIComponent(inventoryName)}`)
	if (cateId != null && cateId != '') queryParts.push(`cateId=${cateId}`)
	if (stepId != null && stepId != '') queryParts.push(`stepId=${stepId}`)
	if (stepCateId != null && stepCateId != '') queryParts.push(`stepCateId=${stepCateId}`)

	const queryString = queryParts.join('&')
	const url = queryString.length > 0 ? `/app/front/inventoryTask/inventoryList?${queryString}` : '/app/front/inventoryTask/inventoryList'
	return Get(url, {})
}

// 获取员工所属项目部
export function getEmployeeProjects() {
	return Get("/app/front/common/multiTenant", {})
}

// 获取部门岗位用户列表
export function getDeptPostUserList(type: string, parentId?: string, deptId?: string, name?: string) {
	const queryParts: string[] = [`type=${type}`]
	if (parentId != null && parentId != '') queryParts.push(`parentId=${parentId}`)
	if (deptId != null && deptId != '') queryParts.push(`deptId=${deptId}`)
	if (name != null && name != '') queryParts.push(`name=${encodeURIComponent(name)}`)

	const queryString = queryParts.join('&')
	return Get(`/app/front/inventoryTask/deptPostUserList?${queryString}`, {})
}