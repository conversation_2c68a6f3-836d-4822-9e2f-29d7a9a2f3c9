# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 200 | 成功 |
| 401 | 未鉴权 |
| 500 | 错误 |

# 数据库资料

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-11 09:26:40

> 更新时间: 2025-07-11 09:26:40

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

## 数据字典

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-11 09:29:20

> 更新时间: 2025-07-11 09:30:50

##### 1.表总览

**[object Object]**

##### 1.gen_table

**[object Object]**

**[object Object]**

##### 2.gen_table_column

**[object Object]**

**[object Object]**

##### 3.sys_app_user

**[object Object]**

**[object Object]**

##### 4.sys_appraise_task

**[object Object]**

**[object Object]**

##### 5.sys_appraise_task_form

**[object Object]**

**[object Object]**

##### 6.sys_appraise_task_node

**[object Object]**

**[object Object]**

##### 7.sys_appraise_task_user

**[object Object]**

**[object Object]**

##### 8.sys_attendance_change_log

**[object Object]**

**[object Object]**

##### 9.sys_attendance_check_record

**[object Object]**

**[object Object]**

##### 10.sys_attendance_group

**[object Object]**

**[object Object]**

##### 11.sys_attendance_group_member

**[object Object]**

**[object Object]**

##### 12.sys_attendance_group_member_config

**[object Object]**

**[object Object]**

##### 13.sys_attendance_location

**[object Object]**

**[object Object]**

##### 14.sys_attendance_recheck

**[object Object]**

**[object Object]**

##### 15.sys_attendance_result

**[object Object]**

**[object Object]**

##### 16.sys_attendance_rule_overtime

**[object Object]**

**[object Object]**

##### 17.sys_attendance_shift

**[object Object]**

**[object Object]**

##### 18.sys_attendance_shift_time

**[object Object]**

**[object Object]**

##### 19.sys_attendance_work_day

**[object Object]**

**[object Object]**

##### 20.sys_client

**[object Object]**

**[object Object]**

##### 21.sys_config

**[object Object]**

**[object Object]**

##### 22.sys_dept

**[object Object]**

**[object Object]**

##### 23.sys_dict_data

**[object Object]**

**[object Object]**

##### 24.sys_dict_type

**[object Object]**

**[object Object]**

##### 25.sys_employee_status_change

**[object Object]**

**[object Object]**

##### 26.sys_fields

**[object Object]**

**[object Object]**

##### 27.sys_flow

**[object Object]**

**[object Object]**

##### 28.sys_flow_conditions

**[object Object]**

**[object Object]**

##### 29.sys_flow_node

**[object Object]**

**[object Object]**

##### 30.sys_flow_node_conditions

**[object Object]**

**[object Object]**

##### 31.sys_flow_node_field

**[object Object]**

**[object Object]**

##### 32.sys_flow_node_user

**[object Object]**

**[object Object]**

##### 33.sys_form_data

**[object Object]**

**[object Object]**

##### 34.sys_forms

**[object Object]**

**[object Object]**

##### 35.sys_group

**[object Object]**

**[object Object]**

##### 36.sys_inventory

**[object Object]**

**[object Object]**

##### 37.sys_inventory_dept

**[object Object]**

**[object Object]**

##### 38.sys_inventory_group

**[object Object]**

**[object Object]**

##### 39.sys_inventory_personnel

**[object Object]**

**[object Object]**

##### 40.sys_inventory_step

**[object Object]**

**[object Object]**

##### 41.sys_logininfor

**[object Object]**

**[object Object]**

##### 42.sys_menu

**[object Object]**

**[object Object]**

##### 43.sys_monthly_assessment

**[object Object]**

**[object Object]**

##### 44.sys_monthly_detail

**[object Object]**

**[object Object]**

##### 45.sys_notice

**[object Object]**

**[object Object]**

##### 46.sys_oper_log

**[object Object]**

**[object Object]**

##### 47.sys_oss

**[object Object]**

**[object Object]**

##### 48.sys_oss_config

**[object Object]**

**[object Object]**

##### 49.sys_post

**[object Object]**

**[object Object]**

##### 50.sys_role

**[object Object]**

**[object Object]**

##### 51.sys_role_dept

**[object Object]**

**[object Object]**

##### 52.sys_role_menu

**[object Object]**

**[object Object]**

##### 53.sys_social

**[object Object]**

**[object Object]**

##### 54.sys_step_dept

**[object Object]**

**[object Object]**

##### 55.sys_task

**[object Object]**

**[object Object]**

##### 56.sys_task_inventory

**[object Object]**

**[object Object]**

##### 57.sys_task_personnel

**[object Object]**

**[object Object]**

##### 58.sys_task_transfer

**[object Object]**

**[object Object]**

##### 59.sys_task_user

**[object Object]**

**[object Object]**

##### 60.sys_task_user_exam

**[object Object]**

**[object Object]**

##### 61.sys_tenant

**[object Object]**

**[object Object]**

##### 62.sys_tenant_package

**[object Object]**

**[object Object]**

##### 63.sys_user

**[object Object]**

**[object Object]**

##### 64.sys_user_cert

**[object Object]**

**[object Object]**

##### 65.sys_user_point

**[object Object]**

**[object Object]**

##### 66.sys_user_post

**[object Object]**

**[object Object]**

##### 67.sys_user_role

**[object Object]**

**[object Object]**

##### 68.sys_weekly_assessment

**[object Object]**

**[object Object]**

##### 69.sys_weekly_assessment_detail

**[object Object]**

**[object Object]**

##### 70.sys_work_cate

**[object Object]**

**[object Object]**

##### 71.sys_work_point

**[object Object]**

**[object Object]**

##### 72.sys_work_step

**[object Object]**

**[object Object]**

##### 73.sys_work_step_cate

**[object Object]**

**[object Object]**

**[object Object]**

**Query**

# 接口文档

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-11 09:26:54

> 更新时间: 2025-07-11 09:26:54

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

## 租户后台

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-11 09:27:43

> 更新时间: 2025-07-11 11:23:40

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | 客户端ID |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | 客户端ID |

**Query**

### 人事考勤

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 11:01:20

> 更新时间: 2025-07-16 11:57:58

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 员工组列表-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 14:05:58

> 更新时间: 2025-07-16 14:09:48

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/group/list?pageNum=1&pageSize=15&groupName=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 15 | string | 是 | 每页条数 |
| groupName | - | string | 否 | 组名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 1,
    "rows": [
        {
            "groupId": "1944968716408115201",
            "groupName": "测试组2",
            "groupSort": 0,
            "remark": "",
            "createTime": "2025-07-15 11:54:12"
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.groupId | 1944968716408115201 | string | 组 |
| rows.groupName | 测试组2 | string | 组名称 |
| rows.groupSort | 0 | number | 组排序 |
| rows.remark | - | string | 备注 |
| rows.createTime | 2025-07-15 11:54:12 | string | 创建 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 员工组列表-不分页 

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 14:09:59

> 更新时间: 2025-07-16 14:09:59

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/group/select?groupName=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | - | string | 是 | 组名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "groupId": "1944968716408115201",
            "groupName": "测试组2",
            "groupSort": 0,
            "remark": "",
            "createTime": "2025-07-15 11:54:12"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.groupId | 1944968716408115201 | string | 组 |
| rows.groupName | 测试组2 | string | 组名称 |
| rows.groupSort | 0 | number | 组排序 |
| rows.remark | - | string | 备注 |
| rows.createTime | 2025-07-15 11:54:12 | string | 创建 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 新增员工组

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 14:11:33

> 更新时间: 2025-07-16 14:14:34

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/group

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"groupName": "测试组",
	"groupSort": "1",
	"remark": "consequat voluptate reprehenderit sit"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | 测试组 | string | 是 | 组名称 |
| groupSort | 1 | string | 否 | 组排序 |
| remark | consequat voluptate reprehenderit sit | string | 否 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 员工组详情

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 14:17:57

> 更新时间: 2025-07-16 14:18:58

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/group/{groupId}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944968716408115201 | string | 是 | 员工组ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "groupId": "1944968716408115201",
        "groupName": "测试组2",
        "groupSort": 0,
        "remark": "",
        "createTime": "2025-07-15 11:54:12"
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.groupId | 1944968716408115201 | string | 员工组ID |
| data.groupName | 测试组2 | string | 组名称 |
| data.groupSort | 0 | number | 显示顺序 |
| data.remark | - | string | 备注 |
| data.createTime | 2025-07-15 11:54:12 | string | 创建 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改员工组

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 14:22:14

> 更新时间: 2025-07-16 14:22:14

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/group

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"groupId": "1945366331618508801",
	"groupName": "测试组",
	"groupSort": 1,
	"remark": "laboris nostrud anim in ex"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | null | 返回数据 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除员工组

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 14:26:41

> 更新时间: 2025-07-16 14:26:41

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/group/{ids}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| ids | 1945366331618508801 | string | 是 | 员工组id组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 岗位列表-不分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 14:48:45

> 更新时间: 2025-07-16 14:49:47

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/post/select

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "postId": "1944678525604741122",
            "postCode": "",
            "postName": "测试岗位1",
            "postCategory": "",
            "postSort": 0,
            "status": "0",
            "remark": "",
            "createTime": "2025-07-14 16:41:06"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.postId | 1944678525604741122 | string | 岗位 |
| data.postCode | - | string | 岗位编码 |
| data.postName | 测试岗位1 | string | 岗位 |
| data.postCategory | - | string | - |
| data.postSort | 0 | number | 显示顺序 降序 |
| data.status | 0 | string | - |
| data.remark | - | string | 备注 |
| data.createTime | 2025-07-14 16:41:06 | string | 创建 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工点列表-不分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 15:46:10

> 更新时间: 2025-07-16 15:46:10

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/workPoint/select

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "pointId": "1945383046872064002",
            "pointName": "一号工点",
            "pointCode": "GDBH001",
            "pointSort": 1,
            "remark": "该工点为重点施工区域",
            "createTime": null
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.pointId | 1945383046872064002 | string | 工点ID |
| data.pointName | 一号工点 | string | 工点名称 |
| data.pointCode | GDBH001 | string | 工点编号 |
| data.pointSort | 1 | number | 显示顺序 降序 |
| data.remark | 该工点为重点施工区域 | string | 备注 |
| data.createTime | - | null | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 花名册列表-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 13:46:57

> 更新时间: 2025-07-22 10:51:35

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser/list?pageSize=15&pageNum=1&nickName=&phonenumber=&sex=&status=&empStatus=&label=&groupId=&workNum=&duties=&education=&employeeType=&beginTime=&endTime=&aGroupId=1946123646491758593

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | string | 是 | 每页条数 |
| pageNum | 1 | string | 是 | 页数 |
| nickName | - | string | 否 | 员工姓名 |
| phonenumber | - | string | 否 | 手机号 |
| sex | - | string | 否 | 性别 0 男 1女 |
| status | - | string | 否 | 状态 0 正常 1停用 |
| empStatus | - | string | 否 | 员工状态（0在职 1离职 2待入职3调岗中4已归档） |
| label | - | string | 否 | 员工标签（0派遣人员 1外协人员） |
| groupId | - | string | 否 | 员工组 |
| workNum | - | string | 否 | 工号 |
| duties | - | string | 否 | 职称 字典数据sys_duties |
| education | - | string | 否 | 学历 字典数据sys_education |
| employeeType | - | string | 否 | 员工类型 字典数据sys_employee_type |
| beginTime | - | string | 否 | 开始时间（入职） |
| endTime | - | string | 否 | 结束时间（入职） |
| deptId | 1945307819278397442 | string | 否 | 部门ID |
| postId | 1944678525604741122 | string | 否 | 岗位ID |
| aGroupId | 1946123646491758593 | string | 否 | 考勤组ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 1,
    "rows": [
        {
            "userId": "1945656251901890561",
            "tenantId": "475158",
            "nickName": "沈勇",
            "phonenumber": "18636984056",
            "sex": "0",
            "status": "0",
            "empStatus": "0",
            "label": "0",
            "groupName": "测试组2",
            "entryTime": "1980-08-29 13:28:42",
            "workNum": "consequat nostrud ut pariatur",
            "deptPostStr": "",
            "agroupName": "固定班考勤1",
            "agroupId": "1946123646491758593"
        }
    ],
    "code": 200,
    "msg": "操作成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.userId | 1945656251901890561 | string | 员工ID |
| rows.tenantId | 475158 | string | - |
| rows.nickName | 沈勇 | string | 员工姓名 |
| rows.phonenumber | 18636984056 | string | 手机号 |
| rows.sex | 0 | string | 性别 0 男 1女 |
| rows.status | 0 | string | 状态 0 正常 1停用 |
| rows.empStatus | 0 | string | 员工状态（0在职 1离职 2待入职3调岗中4已归档） |
| rows.label | 0 | string | 员工标签（0派遣人员 1外协人员） |
| rows.groupName | 测试组2 | string | 组名称 |
| rows.entryTime | 1980-08-29 13:28:42 | string | 入职时间 |
| rows.workNum | consequat nostrud ut pariatur | string | 工号 |
| rows.deptPostStr | - | string | - |
| rows.agroupName | 固定班考勤1 | string | 考勤组名称 |
| rows.agroupId | 1946123646491758593 | string | - |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 花名册列表-不分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-24 08:48:17

> 更新时间: 2025-07-24 08:48:26

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser/select?nickName=&phonenumber=&sex=&status=&empStatus=&label=&groupId=&workNum=&duties=&education=&employeeType=&beginTime=&endTime=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| deptId | 1945307819278397442 | string | 否 | 部门ID |
| postId | 1944678525604741122 | string | 否 | 岗位ID |
| aGroupId | 1946123646491758593 | string | 否 | 考勤组ID |
| nickName | - | string | 否 | 员工姓名 |
| phonenumber | - | string | 否 | 手机号 |
| sex | - | string | 否 | 性别 0 男 1女 |
| status | - | string | 否 | 状态 0 正常 1停用 |
| empStatus | - | string | 否 | 员工状态（0在职 1离职 2待入职3调岗中4已归档） |
| label | - | string | 否 | 员工标签（0派遣人员 1外协人员） |
| groupId | - | string | 否 | 员工组 |
| workNum | - | string | 否 | 工号 |
| duties | - | string | 否 | 职称 字典数据sys_duties |
| education | - | string | 否 | 学历 字典数据sys_education |
| employeeType | - | string | 否 | 员工类型 字典数据sys_employee_type |
| beginTime | - | string | 否 | 开始时间（入职） |
| endTime | - | string | 否 | 结束时间（入职） |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "userId": "1947839522658160641",
            "tenantId": "475158",
            "nickName": "测试",
            "phonenumber": "18434910600",
            "sex": "0",
            "status": "0",
            "empStatus": "0",
            "label": "0",
            "groupName": "测试组1",
            "entryTime": "2025-07-21 00:00:00",
            "workNum": "ceshi-01",
            "deptPostStr": "测试-技术主管,测试-研发主管",
            "agroupName": "固定班考勤",
            "agroupId": "1946108574675566593"
        },
        {
            "userId": "1947839821644926978",
            "tenantId": "475158",
            "nickName": "测试",
            "phonenumber": "18434910601",
            "sex": "0",
            "status": "0",
            "empStatus": "0",
            "label": "0",
            "groupName": "测试组1",
            "entryTime": "2020-06-15 00:00:00",
            "workNum": "ceshi-02",
            "deptPostStr": "测试-技术主管,测试-研发主管",
            "agroupName": "固定班考勤1",
            "agroupId": "1946123646491758593"
        },
        {
            "userId": "1945656251901890561",
            "tenantId": "475158",
            "nickName": "沈勇",
            "phonenumber": "18636984056",
            "sex": "0",
            "status": "0",
            "empStatus": "0",
            "label": "0",
            "groupName": "测试组2",
            "entryTime": "1980-08-29 13:28:42",
            "workNum": "consequat nostrud ut pariatur",
            "deptPostStr": "",
            "agroupName": "固定班考勤1",
            "agroupId": "1946123646491758593"
        },
        {
            "userId": "1947946058292035585",
            "tenantId": "475158",
            "nickName": "hsy",
            "phonenumber": "13412341234",
            "sex": "0",
            "status": "0",
            "empStatus": "0",
            "label": "0",
            "groupName": "测试组2",
            "entryTime": null,
            "workNum": "123",
            "deptPostStr": "第一项目部-技术主管",
            "agroupName": "-",
            "agroupId": null
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.userId | 1947839522658160641 | string | - |
| data.tenantId | 475158 | string | - |
| data.nickName | 测试 | string | - |
| data.phonenumber | 18434910600 | string | - |
| data.sex | 0 | string | - |
| data.status | 0 | string | - |
| data.empStatus | 0 | string | - |
| data.label | 0 | string | - |
| data.groupName | 测试组1 | string | - |
| data.entryTime | 2025-07-21 00:00:00 | string | - |
| data.workNum | ceshi-01 | string | - |
| data.deptPostStr | 测试-技术主管,测试-研发主管 | string | - |
| data.agroupName | 固定班考勤 | string | - |
| data.agroupId | 1946108574675566593 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 添加员工

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 16:04:52

> 更新时间: 2025-07-22 16:20:30

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"nickName": "沈勇",
	"phonenumber": "18636984056",
	"sex": "0",
	"password": "xdc@xdc1",
	"label": "0",
	"groupId": "1944968716408115201",
	"workNum": "consequat nostrud ut pariatur",
	"entryTime": "1980-08-29 13:28:42",
	"birthday": "1976-02-23",
	"duties": "1",
	"graduationSchool": "in eu elit",
	"workYear": 66,
	"education": "1",
	"employeeType": "1",
	"pointIds": [
		"1945383046872064002"
	],
	"deptPosts": [
        {
			"deptId": "1945307819278397441",
			"postId": "1944678525604741122",
			"dataScope":"1"
		}
	],
	"aGroupId":"1946108574675566593"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| nickName | 沈勇 | string | 是 | 员工姓名 |
| phonenumber | 18636984056 | string | 是 | 手机号 |
| sex | 0 | string | 是 | 用户性别（0男 1女 ） |
| password | xdc@xdc1 | string | 是 | 密码 |
| label | 0 | string | 是 | 员工标签（0派遣人员 1外协人员） |
| groupId | 1944968716408115201 | string | 是 | 所属员工主 |
| workNum | consequat nostrud ut pariatur | string | 是 | 工号 |
| entryTime | 1980-08-29 13:28:42 | string | 否 | 入职时间 |
| birthday | 1976-02-23 | string | 否 | 生日（ 日期格式） |
| duties | 1 | string | 否 | 职称 |
| graduationSchool | in eu elit | string | 否 | 毕业学校 |
| workYear | 66 | number | 否 | 工作年限 |
| education | 1 | string | 否 | 学历 |
| employeeType | 1 | string | 否 | 员工类型 |
| pointIds | - | array | 否 | 工点ID组 |
| deptPosts | - | array | 否 | - |
| deptPosts.deptId | 1945307819278397441 | string | 否 | 部门ID |
| deptPosts.postId | 1944678525604741122 | string | 否 | 岗位ID |
| deptPosts.dataScope | 1 | string | 否 | 数据范围（  1：本部门数据权限 2：本部门及以下数据权限 3：仅本人数据权限 4：部门及以下或本人数据权限 |
| aGroupId | 1946108574675566593 | string | 否 | 考勤组ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | null | 返回数据 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 员工详情

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 09:28:53

> 更新时间: 2025-07-24 13:04:44

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser/{userId}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| userId | 1945656251901890561 | string | 是 | 员工ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "userId": "1945647273083981825",
        "tenantId": "475158",
        "userName": "18636984056",
        "nickName": "沈勇",
        "phonenumber": "18636984056",
        "sex": "0",
        "status": "0",
        "empStatus": "0",
        "label": "0",
        "groupId": "1944968716408115201",
        "groupName": null,
        "entryTime": "1980-08-29 13:28:42",
        "workNum": "consequat nostrud ut pariatur",
        "remark": null,
        "birthday": "1976-02-23",
        "duties": "incididunt irure",
        "graduationSchool": "in eu elit",
        "workYear": 66,
        "education": "ut in in laboris ipsum",
        "employeeType": "in Ut labore laboris",
        "pointIds": [
            "1945383046872064002"
        ],
        "deptPosts": [
            {
                "frontUserId": "1945647273083981825",
                "deptId": "1945307819278397441",
                "postId": "1944678525604741122",
                "dataScope" :"1"
            }
        ]
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.userId | 1945647273083981825 | string | 员工ID |
| data.tenantId | 475158 | string | - |
| data.userName | 18636984056 | string | 员工账号 |
| data.nickName | 沈勇 | string | 员工 |
| data.phonenumber | 18636984056 | string | 手机号 |
| data.sex | 0 | string | 性别 0男1女 |
| data.status | 0 | string | 状态0 正常 1停用 |
| data.empStatus | 0 | string | - |
| data.label | 0 | string | - |
| data.groupId | 1944968716408115201 | string | 员工组ID |
| data.groupName | - | null | 组名称 |
| data.entryTime | 1980-08-29 13:28:42 | string | - |
| data.workNum | consequat nostrud ut pariatur | string | - |
| data.remark | - | null | 备注 |
| data.birthday | 1976-02-23 | string | - |
| data.duties | incididunt irure | string | - |
| data.graduationSchool | in eu elit | string | - |
| data.workYear | 66 | number | - |
| data.education | ut in in laboris ipsum | string | - |
| data.employeeType | in Ut labore laboris | string | - |
| data.pointIds | - | array | 工点ID组 |
| data.deptPosts | - | array | - |
| data.deptPosts.frontUserId | 1945647273083981825 | string | 部门 |
| data.deptPosts.deptId | 1945307819278397441 | string | 部门ID |
| data.deptPosts.postId | 1944678525604741122 | string | 岗位ID |
| data.deptPosts.dataScope | 1 | string | 数据范围（  1：本部门数据权限 2：本部门及以下数据权限 3：仅本人数据权限 4：部门及以下或本人数据权限 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改员工账号状态

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 14:32:06

> 更新时间: 2025-07-17 14:32:16

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser/changeStatus

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"userId": "1945656251901890561",
	"status": "1"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| userId | 1945656251901890561 | string | 否 | 员工ID |
| status | 1 | string | 否 | 状态 0 正常 1停用 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 重置密码

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 09:45:03

> 更新时间: 2025-07-17 09:45:03

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser/resetPwd

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"userId": "1945656251901890561",
	"password": "xdc@xdc1"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | null | 返回数据 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改员工

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 10:11:49

> 更新时间: 2025-07-24 13:04:44

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"userId":"1945656251901890561",
	"nickName": "沈勇",
	"phonenumber": "18636984056",
	"sex": "0",
	"label": "0",
	"groupId": "1944968716408115201",
	"workNum": "consequat nostrud ut pariatur",
	"entryTime": "1980-08-29 13:28:42",
	"birthday": "1976-02-23",
	"duties": "1",
	"graduationSchool": "in eu elit",
	"workYear": 66,
	"education": "1",
	"employeeType": "1",
	"pointIds": [
		"1945383046872064002"
	],
	"deptPosts": [
        {
			"deptId": "1945307819278397442",
			"postId": "1944678525604741122",
			"dataScope":"1"
		}
	]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| userId | 1945656251901890561 | string | 是 | 员工ID |
| nickName | 沈勇 | string | 是 | 员工姓名 |
| phonenumber | 18636984056 | string | 是 | 手机号 |
| sex | 0 | string | 是 | 用户性别（0男 1女 ） |
| label | 0 | string | 是 | 员工标签（0派遣人员 1外协人员） |
| groupId | 1944968716408115201 | string | 是 | 所属员工主 |
| workNum | consequat nostrud ut pariatur | string | 是 | 工号 |
| entryTime | 1980-08-29 13:28:42 | string | 否 | 入职时间 |
| birthday | 1976-02-23 | string | 否 | 生日（ 日期格式） |
| duties | 1 | string | 否 | 职称 |
| graduationSchool | in eu elit | string | 否 | 毕业学校 |
| workYear | 66 | number | 否 | 工作年限 |
| education | 1 | string | 否 | 学历 |
| employeeType | 1 | string | 否 | 员工类型 |
| pointIds | - | array | 否 | 工点ID组 |
| deptPosts | - | array | 否 | - |
| deptPosts.deptId | 1945307819278397442 | string | 否 | 部门ID |
| deptPosts.postId | 1944678525604741122 | string | 否 | 岗位ID |
| deptPosts.dataScope | 1 | string | 是 | 数据范围（ 1：本部门数据权限 2：本部门及以下数据权限 3：仅本人数据权限 4：部门及以下或本人数据权限 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | null | 返回数据 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 员工人事变动记录-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 10:41:31

> 更新时间: 2025-07-17 10:41:31

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/userChangeLog/list?pageSize=15&pageNum=1&userName=&workName=&phonenumber=&beginTime=&endTime=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | string | 是 | 每页条数 |
| pageNum | 1 | string | 是 | 页数 |
| userName | - | string | 否 | 员工姓名 |
| workName | - | string | 否 | 员工工号 |
| phonenumber | - | string | 否 | 手机号 |
| beginTime | - | string | 否 | 开始时间 |
| endTime | - | string | 否 | 结束时间 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 2,
    "rows": [
        {
            "changeId": "1945647284647677954",
            "userId": "1945647273083981825",
            "userName": "沈勇",
            "workName": "consequat nostrud ut pariatur",
            "phonenumber": "18636984056",
            "originalStatus": "无",
            "newStatus": "测试-测试岗位1",
            "changeType": "1",
            "reason": "系统录入",
            "applyTime": "2025-07-17 08:50:36",
            "applyDeptId": "1945307819278397441",
            "currentApproverId": null,
            "completionTime": "2025-07-17 08:50:36"
        },
        {
            "changeId": "1945667618859958273",
            "userId": "1945656251901890561",
            "userName": "沈勇",
            "workName": "consequat nostrud ut pariatur",
            "phonenumber": "18636984056",
            "originalStatus": "测试-测试岗位1",
            "newStatus": "测试1-测试岗位1",
            "changeType": "3",
            "reason": "系统调动",
            "applyTime": "2025-07-17 10:11:24",
            "applyDeptId": "1945307819278397442",
            "currentApproverId": null,
            "completionTime": "2025-07-17 10:11:24"
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.changeId | 1945647284647677954 | string | - |
| rows.userName | 沈勇 | string | 员工姓名 |
| rows.workName | consequat nostrud ut pariatur | string | 员工工号 |
| rows.phonenumber | 18636984056 | string | 手机号 |
| rows.originalStatus | 无 | string | 原状态 |
| rows.newStatus | 测试-测试岗位1 | string | 新状态 |
| rows.changeType | 1 | string | 变动类型：1(入职)、2(离职)、3(调动) 4 (归档) |
| rows.reason | 系统录入 | string | 变动原因 |
| rows.applyTime | 2025-07-17 08:50:36 | string | 申请时间 |
| rows.completionTime | 2025-07-17 08:50:36 | string | 完成时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 清空人事变动记录

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 10:48:12

> 更新时间: 2025-07-17 10:48:12

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/userChangeLog/clean

**请求方式**

> DELETE

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 批量删除人事变动记录

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 10:49:01

> 更新时间: 2025-07-17 10:49:01

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/userChangeLog/{changeIds}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| changeIds | - | string | 是 | 人事变动记录ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改员工状态

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 15:10:25

> 更新时间: 2025-07-17 15:10:34

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontUser/changeEmpStatus

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"userId": "1945656251901890561",
	"empStatus": "0"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| userId | 1945656251901890561 | string | 否 | 员工ID |
| empStatus | - | string | 否 | 员工状态（0在职 1离职 4已归档） |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | null | 返回数据 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 新增班次

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-18 09:23:20

> 更新时间: 2025-07-24 08:43:56

**🕘 时间段设置规则说明,一、上班时间与下班时间 (startTime 与 endTime)
    •	startTime 表示正式上班时间，endTime 表示正式下班时间。
    •	这两个时间必须设置，并且：
    •	必须在同一天内；
    •	startTime 与 endTime 的间隔时间不得超过 12 小时。,⸻,二、上班打卡时间段 (checkInStart 与 checkInEnd),用于控制员工在哪个时间段内允许打上班卡：**

**上班打卡开始时间（checkInStart）, •	必须早于 startTime；, •	最早可提前 startTime 8小时以内；, •	不可晚于 startTime。**

**例如：若 startTime 为 09:00，则 checkInEnd 合法范围为 09:00 ~ 09:08:59。**

**接口状态**

> 开发中

**接口URL**

> /system/shift

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"name": "测试班次",
	"lateMinutes": 0,
	"absentMinutes": 0,
	"shiftTimes": [
		{
			"startTime": "09:00",
			"endTime": "18:00",
			"checkInStart": "08:59",
			"checkInEnd": "09:01",
			"checkOutStart": "17:59",
			"checkOutEnd": "18:01"
		}
		
	]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | 测试班次 | string | 是 | 班次名称 |
| lateMinutes | 0 | number | 是 | 严重迟到阈值 0 未设置 |
| absentMinutes | 0 | number | 是 | 旷工迟到阈值 0 未设置 |
| shiftTimes | - | array | 否 | - |
| shiftTimes.startTime | 09:00 | string | 是 | 上班时间 |
| shiftTimes.endTime | 18:00 | string | 是 | 下班时间 |
| shiftTimes.checkInStart | 08:59 | string | 否 | 上班打卡开始时间 时间类型1 用 |
| shiftTimes.checkInEnd | 09:01 | string | 否 | 上班打卡结束时间 时间类型1 用 |
| shiftTimes.checkOutStart | 17:59 | string | 否 | 下班打卡开始 时间类型1 用 |
| shiftTimes.checkOutEnd | 18:01 | string | 否 | 下班打卡结束时间 时间类型1 用 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 班次列表-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-18 09:39:12

> 更新时间: 2025-07-18 09:59:44

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/shift/list?pageSize=15&pageNum=1&name=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | string | 是 | 每页条数 |
| pageNum | 1 | string | 是 | 页码 |
| name | - | string | 否 | 班次名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 1,
    "rows": [
        {
            "id": "1946020398216839169",
            "name": "测试班次",
            "lateMinutes": 0,
            "absentMinutes": 0,
            "shiftTimes": [
                {
                    "id": "1946020398313308162",
                    "shiftId": "1946020398216839169",
                    "isSpan": "0",
                    "startTime": "09:00:00",
                    "endTime": "18:00:00",
                    "checkInStart": "08:59:00",
                    "checkInEnd": "09:01:00",
                    "checkOutStart": "17:59:00",
                    "checkOutEnd": "18:01:00"
                }
            ]
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.id | 1946020398216839169 | string | 班次ID |
| rows.name | 测试班次 | string | 班次名称 |
| rows.lateMinutes | 0 | number | - |
| rows.absentMinutes | 0 | number | - |
| rows.shiftTimes | - | array | - |
| rows.shiftTimes.id | 1946020398313308162 | string | - |
| rows.shiftTimes.shiftId | 1946020398216839169 | string | 班次ID |
| rows.shiftTimes.isSpan | 0 | string | 是否次日 0 否 1 |
| rows.shiftTimes.startTime | 09:00:00 | string | 上班时间 |
| rows.shiftTimes.endTime | 18:00:00 | string | 下班时间 |
| rows.shiftTimes.checkInStart | 08:59:00 | string | 上班打卡开始时间 |
| rows.shiftTimes.checkInEnd | 09:01:00 | string | 上班打卡结束时间 |
| rows.shiftTimes.checkOutStart | 17:59:00 | string | 下班打卡开始 |
| rows.shiftTimes.checkOutEnd | 18:01:00 | string | 下班打卡结束时间 时间类型1 用 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 班次详情

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-18 09:51:46

> 更新时间: 2025-07-18 09:51:46

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/shift/{id}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| id | 1946020398216839169 | string | 是 | 班次ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": "1946020398216839169",
        "name": "测试班次",
        "lateMinutes": 0,
        "absentMinutes": 0,
        "shiftTimes": [
            {
                "id": "1946020398313308162",
                "shiftId": "1946020398216839169",
                "isSpan": "0",
                "startTime": "09:00:00",
                "endTime": "18:00:00",
                "checkInStart": "08:59:00",
                "checkInEnd": "09:01:00",
                "checkOutStart": "17:59:00",
                "checkOutEnd": "18:01:00"
            }
        ]
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.id | 1946020398216839169 | string | 班次ID |
| data.name | 测试班次 | string | 班次名称 |
| data.lateMinutes | 0 | number | 严重迟到阈值 0 未设置 |
| data.absentMinutes | 0 | number | 旷工迟到阈值 0 未设置 |
| data.shiftTimes | - | array | 班次打卡时间段 |
| data.shiftTimes.id | 1946020398313308162 | string | - |
| data.shiftTimes.shiftId | 1946020398216839169 | string | 班次ID |
| data.shiftTimes.isSpan | 0 | string | 是否次日 0 否1是 |
| data.shiftTimes.startTime | 09:00:00 | string | 上班时间 |
| data.shiftTimes.endTime | 18:00:00 | string | 下班时间 |
| data.shiftTimes.checkInStart | 08:59:00 | string | 上班打卡开始时间 |
| data.shiftTimes.checkInEnd | 09:01:00 | string | 上班打卡结束时间 |
| data.shiftTimes.checkOutStart | 17:59:00 | string | 下班打卡开始 时间类型1 用 |
| data.shiftTimes.checkOutEnd | 18:01:00 | string | 下班打卡结束时间 时间类型1 用 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 班次列表-不分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-18 14:54:34

> 更新时间: 2025-07-18 15:31:26

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/shift/select

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
[
    {
        "id": "1946020398216839169",
        "name": "测试班次",
        "lateMinutes": 0,
        "absentMinutes": 0,
        "shiftTimes": [
            {
                "id": "1946020398313308162",
                "shiftId": "1946020398216839169",
                "isSpan": "0",
                "startTime": "09:00:00",
                "endTime": "18:00:00",
                "checkInStart": "08:59:00",
                "checkInEnd": "09:01:00",
                "checkOutStart": "17:59:00",
                "checkOutEnd": "18:01:00"
            }
        ]
    }
]
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| 0 | - | object | - |
| 0.id | 1946020398216839169 | string | 班次ID |
| 0.name | 测试班次 | string | 班次名称 |
| 0.lateMinutes | 0 | number | - |
| 0.absentMinutes | 0 | number | - |
| 0.shiftTimes | - | array | - |
| 0.shiftTimes.id | 1946020398313308162 | string | - |
| 0.shiftTimes.shiftId | 1946020398216839169 | string | 班次ID |
| 0.shiftTimes.isSpan | 0 | string | 是否次日 0 否 1是 |
| 0.shiftTimes.startTime | 09:00:00 | string | 上班时间 |
| 0.shiftTimes.endTime | 18:00:00 | string | 下班时间 |
| 0.shiftTimes.checkInStart | 08:59:00 | string | 上班打卡开始时间 |
| 0.shiftTimes.checkInEnd | 09:01:00 | string | 上班打卡结束时间 |
| 0.shiftTimes.checkOutStart | 17:59:00 | string | 下班打卡开始 |
| 0.shiftTimes.checkOutEnd | 18:01:00 | string | 下班打卡结束时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除班次

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-21 14:28:39

> 更新时间: 2025-07-21 14:30:09

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/shift/{ids}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| ids | 1946020398216839169 | string | 是 | 班次ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 500,
    "msg": "班次有使用的考勤组，无法删除",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 新增考勤组

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-18 10:16:51

> 更新时间: 2025-07-18 16:25:19

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceGroup

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"name": "固定班考勤1",
	"blockType": "1,2",
	"type": "1",
	"shiftIds": "in laboris in quis cupidatat",
	"changCycle": 30,
	"locationConfig": [
		{
			"locationName": "山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局",
			"longitude": "110.993466",
			"latitude": "35.008534",
			"radius": 100,
			"address": "山西省运城市盐湖区南城街道西城墙街日"
		}
	],
	"memberConfig": [
		{
			"assignType": "1",
			"relationId": "1945307819278397441"
		}
	],
	"workDayConfig": [
		{
			"weekDay": 1,
			"shiftId": "1946020398216839169"
		},
		{
			"weekDay": 1,
			"shiftId": "1946020398216839169"
		},
		{
			"weekDay": 1,
			"shiftId": "1946020398216839169"
		}
	],
	"replaceUserIds": [
		"1945656251901890561"
	
	]
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 检测考勤人员是否存在于其他组

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-18 15:54:35

> 更新时间: 2025-07-18 16:07:37

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceGroup/checkMemberExist

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
[
	{
		"assignType": "1",
		"relationId": "1945307819278397441"
	}
	
]
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "isExist": true,
        "exitsList": [
            {
                "userId": "1945656251901890561",
                "groupId": "1946108574675566593",
                "name": "固定班考勤"
            }
        ]
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.isExist | true | boolean | 是否存在 |
| data.exitsList | - | array | 存在人员列表 |
| data.exitsList.userId | 1945656251901890561 | string | 员工 |
| data.exitsList.groupId | 1946108574675566593 | string | 考勤组id |
| data.exitsList.name | 固定班考勤 | string | 考勤组名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 考勤组详情

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-18 16:55:23

> 更新时间: 2025-07-18 17:33:36

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceGroup/{id}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| id | 1946108574675566593 | string | 是 | 考勤组ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": "1946108574675566593",
        "name": "固定班考勤",
        "blockType": "1,2",
        "overtimeRoleId": 0,
        "fieldworkRole": null,
        "type": "1",
        "shiftIds": "",
        "changCycle": 30,
        "locationConfig": [
            {
                "id": "1946108574784618498",
                "tenantId": "475158",
                "groupId": "1946108574675566593",
                "locationName": "山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局",
                "longitude": 110.993466,
                "latitude": 35.008534,
                "radius": 100,
                "address": "山西省运城市盐湖区南城街道西城墙街日"
            },
            {
                "id": "1946108574797201409",
                "tenantId": "475158",
                "groupId": "1946108574675566593",
                "locationName": "山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局",
                "longitude": 110.993466,
                "latitude": 35.008534,
                "radius": 100,
                "address": "山西省运城市盐湖区南城街道西城墙街日"
            },
            {
                "id": "1946108574797201410",
                "tenantId": "475158",
                "groupId": "1946108574675566593",
                "locationName": "山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局",
                "longitude": 110.993466,
                "latitude": 35.008534,
                "radius": 100,
                "address": "山西省运城市盐湖区南城街道西城墙街日"
            },
            {
                "id": "1946108574797201411",
                "tenantId": "475158",
                "groupId": "1946108574675566593",
                "locationName": "山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局",
                "longitude": 110.993466,
                "latitude": 35.008534,
                "radius": 100,
                "address": "山西省运城市盐湖区南城街道西城墙街日"
            }
        ],
        "memberConfig": [
            {
                "id": "1946108574931419138",
                "tenantId": "475158",
                "groupId": "1946108574675566593",
                "assignType": "1",
                "relationId": "1945307819278397441"
            }
        ],
        "workDayConfig": [
            {
                "id": "1946108575061442561",
                "groupId": "1946108574675566593",
                "weekDay": 1,
                "shiftId": "1946020398216839169",
                "shiftStr": "班次 测试班次：09:00-18:00"
            },
            {
                "id": "1946108575065636866",
                "groupId": "1946108574675566593",
                "weekDay": 2,
                "shiftId": "1946020398216839169",
                "shiftStr": "班次 测试班次：09:00-18:00"
            },
            {
                "id": "1946108575069831170",
                "groupId": "1946108574675566593",
                "weekDay": 3,
                "shiftId": "1946020398216839169",
                "shiftStr": "班次 测试班次：09:00-18:00"
            }
        ]
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.id | 1946108574675566593 | string | 考勤组ID |
| data.name | 固定班考勤 | string | 考勤名称 |
| data.blockType | 1,2 | string | 打开方式 1GPS定位 2 拍照打卡 ，号分割 |
| data.overtimeRoleId | 0 | number | - |
| data.fieldworkRole | - | null | - |
| data.type | 1 | string | 考勤类型 1固定班制 2排班制 |
| data.shiftIds | - | string | 考勤班次ID (排班制用) ,号分割 |
| data.changCycle | 30 | number | 换班周期（天）（排班制用） |
| data.locationConfig | - | array | 打卡地点配置 |
| data.locationConfig.id | 1946108574784618498 | string | 考勤组ID |
| data.locationConfig.tenantId | 475158 | string | - |
| data.locationConfig.groupId | 1946108574675566593 | string | - |
| data.locationConfig.locationName | 山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局 | string | 地点 |
| data.locationConfig.longitude | 110.993466 | number | 经度 |
| data.locationConfig.latitude | 35.008534 | number | 纬度 |
| data.locationConfig.radius | 100 | number | 范围 |
| data.locationConfig.address | 山西省运城市盐湖区南城街道西城墙街日 | string | 地点地址 |
| data.memberConfig | - | array | 考勤人员配置 |
| data.memberConfig.id | 1946108574931419138 | string | 考勤组ID |
| data.memberConfig.tenantId | 475158 | string | - |
| data.memberConfig.groupId | 1946108574675566593 | string | - |
| data.memberConfig.assignType | 1 | string | 分配类型 1部门分配 2 岗位分配  3人员分配 |
| data.memberConfig.relationId | 1945307819278397441 | string | 关联ID |
| data.workDayConfig | - | array | 考勤工作日配置 (固定班) |
| data.workDayConfig.id | 1946108575061442561 | string | 考勤组ID |
| data.workDayConfig.groupId | 1946108574675566593 | string | - |
| data.workDayConfig.weekDay | 1 | number | 工作日1-7 代表周一到周日 |
| data.workDayConfig.shiftId | 1946020398216839169 | string | 班次ID |
| data.workDayConfig.shiftStr | 班次 测试班次：09:00-18:00 | string | 班次 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 考勤组列表-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-21 14:33:01

> 更新时间: 2025-07-21 15:02:13

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceGroup/list?pageSize=15&pageNum=1&name=&type=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | string | 是 | 每页条数 |
| pageNum | 1 | string | 是 | 页码 |
| name | - | string | 否 | 考勤组名称 |
| type | - | string | 否 | 考勤类型 1固定班制 2排班制 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 2,
    "rows": [
        {
            "id": "1946108574675566593",
            "name": "固定班考勤",
            "blockType": "1,2",
            "overtimeRoleId": 0,
            "fieldworkRole": null,
            "type": "1",
            "shiftIds": "",
            "shiftName": null,
            "changCycle": 30,
            "locationConfig": [],
            "memberConfig": [],
            "workDayConfig": [
                {
                    "id": "1946108575061442561",
                    "groupId": "1946108574675566593",
                    "weekDay": 1,
                    "shiftId": "1946020398216839169",
                    "shiftStr": "班次 测试班次：09:00-18:00"
                },
                {
                    "id": "1946108575065636866",
                    "groupId": "1946108574675566593",
                    "weekDay": 2,
                    "shiftId": "1946020398216839169",
                    "shiftStr": "班次 测试班次：09:00-18:00"
                },
                {
                    "id": "1946108575069831170",
                    "groupId": "1946108574675566593",
                    "weekDay": 3,
                    "shiftId": "1946020398216839169",
                    "shiftStr": "班次 测试班次：09:00-18:00"
                }
            ],
            "groupPeople": 0
        },
        {
            "id": "1946123646491758593",
            "name": "固定班考勤1",
            "blockType": "1,2",
            "overtimeRoleId": 0,
            "fieldworkRole": null,
            "type": "1",
            "shiftIds": "",
            "shiftName": null,
            "changCycle": 30,
            "locationConfig": [],
            "memberConfig": [],
            "workDayConfig": [
                {
                    "id": "1946123691802824705",
                    "groupId": "1946123646491758593",
                    "weekDay": 1,
                    "shiftId": "1946020398216839169",
                    "shiftStr": "班次 测试班次：09:00-18:00"
                },
                {
                    "id": "1946123691916070914",
                    "groupId": "1946123646491758593",
                    "weekDay": 2,
                    "shiftId": "1946020398216839169",
                    "shiftStr": "班次 测试班次：09:00-18:00"
                },
                {
                    "id": "1946123691928653826",
                    "groupId": "1946123646491758593",
                    "weekDay": 3,
                    "shiftId": "1946020398216839169",
                    "shiftStr": "班次 测试班次：09:00-18:00"
                }
            ],
            "groupPeople": 1
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.id | 1946108574675566593 | string | - |
| rows.name | 固定班考勤 | string | 考勤组名称 |
| rows.blockType | 1,2 | string | 打开方式 1GPS定位 2 拍照打卡 |
| rows.overtimeRoleId | 0 | number | - |
| rows.fieldworkRole | - | null | - |
| rows.type | 1 | string | 考勤类型 1固定班制 2排班制 |
| rows.shiftIds | - | string | 考勤班次ID (排班制用) ,号分割 |
| rows.shiftName | - | null | 考勤班次名称（排班制用） |
| rows.changCycle | 30 | number | 换班周期（天）排班制用考勤工作日配置 |
| rows.locationConfig | - | array | - |
| rows.memberConfig | - | array | - |
| rows.workDayConfig | - | array | 考勤工作日配置 |
| rows.workDayConfig.id | 1946108575061442561 | string | - |
| rows.workDayConfig.groupId | 1946108574675566593 | string | - |
| rows.workDayConfig.weekDay | 1 | number | - |
| rows.workDayConfig.shiftId | 1946020398216839169 | string | - |
| rows.workDayConfig.shiftStr | 班次 测试班次：09:00-18:00 | string | - |
| rows.groupPeople | 0 | number | - |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 岗位前台选项列表（新增编辑岗位用）

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-22 08:34:51

> 更新时间: 2025-07-22 10:06:23

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/frontPermissions/select

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "1946108575065636883",
            "parentId": 0,
            "name": "员工打卡",
            "roleKey": "system:frontUserCheckIn"
        },
        {
            "id": "1946108575065636929",
            "parentId": 0,
            "name": "员工调整",
            "roleKey": "system:frontUserChange"
        },
        {
            "id": "1946108575065636974",
            "parentId": 0,
            "name": "任务下发",
            "roleKey": "system:frontUserTask"
        },
        {
            "id": "1946108575065637002",
            "parentId": 0,
            "name": "日报评价",
            "roleKey": "system:frontUserDay"
        },
        {
            "id": "1946108575065637047",
            "parentId": 0,
            "name": "周考评价",
            "roleKey": "system:frontUserWeek"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.id | 1946108575065636883 | string | 权限ID |
| data.parentId | 0 | number | - |
| data.name | 员工打卡 | string | 权限名称 |
| data.roleKey | system:frontUserCheckIn | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 考勤组列表-不分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-22 10:35:49

> 更新时间: 2025-07-22 10:35:49

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceGroup/select?deptIds=1945307819278397441,1945307819278397442&postIds=1947193690327937026

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| deptIds | 1945307819278397441,1945307819278397442 | array | 否 | 部门ID组 |
| postIds | 1947193690327937026 | array | 否 | 岗位ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "1946108574675566593",
            "name": "固定班考勤",
            "blockType": "1,2",
            "overtimeRoleId": 0,
            "fieldworkRole": null,
            "type": "1",
            "shiftIds": "",
            "shiftName": null,
            "changCycle": 30,
            "locationConfig": [],
            "memberConfig": [],
            "workDayConfig": [],
            "groupPeople": null
        },
        {
            "id": "1946123646491758593",
            "name": "固定班考勤1",
            "blockType": "1,2",
            "overtimeRoleId": 0,
            "fieldworkRole": null,
            "type": "1",
            "shiftIds": "",
            "shiftName": null,
            "changCycle": 30,
            "locationConfig": [],
            "memberConfig": [],
            "workDayConfig": [],
            "groupPeople": null
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.id | 1946108574675566593 | string | 考勤组 |
| data.name | 固定班考勤 | string | 考勤组名称 |
| data.blockType | 1,2 | string | - |
| data.overtimeRoleId | 0 | number | - |
| data.fieldworkRole | - | null | - |
| data.type | 1 | string | - |
| data.shiftIds | - | string | - |
| data.shiftName | - | null | - |
| data.changCycle | 30 | number | - |
| data.locationConfig | - | array | - |
| data.memberConfig | - | array | - |
| data.workDayConfig | - | array | - |
| data.groupPeople | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改班次

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-24 09:03:09

> 更新时间: 2025-07-24 09:03:09

**🕘 时间段设置规则说明,一、上班时间与下班时间 (startTime 与 endTime)
    •	startTime 表示正式上班时间，endTime 表示正式下班时间。
    •	这两个时间必须设置，并且：
    •	必须在同一天内；
    •	startTime 与 endTime 的间隔时间不得超过 12 小时。,⸻,二、上班打卡时间段 (checkInStart 与 checkInEnd),用于控制员工在哪个时间段内允许打上班卡：**

**上班打卡开始时间（checkInStart）, •	必须早于 startTime；, •	最早可提前 startTime 8小时以内；, •	不可晚于 startTime。**

**例如：若 startTime 为 09:00，则 checkInEnd 合法范围为 09:00 ~ 09:08:59。**

**接口状态**

> 开发中

**接口URL**

> /system/shift

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"id":"1948184472975364098",
	"name": "测试班次",
	"lateMinutes": 0,
	"absentMinutes": 0,
	"shiftTimes": [
		{
			"startTime": "09:00",
			"endTime": "18:00",
			"checkInStart": "08:59",
			"checkInEnd": "09:01",
			"checkOutStart": "17:59",
			"checkOutEnd": "18:01"
		}
		
	]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| id | 1948184472975364098 | string | 是 | 班次ID |
| name | 测试班次 | string | 是 | 班次名称 |
| lateMinutes | 0 | number | 是 | 严重迟到阈值 0 未设置 |
| absentMinutes | 0 | number | 是 | 旷工迟到阈值 0 未设置 |
| shiftTimes | - | array | 否 | - |
| shiftTimes.startTime | 09:00 | string | 是 | 上班时间 |
| shiftTimes.endTime | 18:00 | string | 是 | 下班时间 |
| shiftTimes.checkInStart | 08:59 | string | 否 | 上班打卡开始时间 |
| shiftTimes.checkInEnd | 09:01 | string | 否 | 上班打卡结束时间 |
| shiftTimes.checkOutStart | 17:59 | string | 否 | 下班打卡开始时间 |
| shiftTimes.checkOutEnd | 18:01 | string | 否 | 下班打卡结束时间 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改考勤组

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-25 16:52:29

> 更新时间: 2025-07-25 16:52:29

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceGroup

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"id":"1946123646491758593",
	"name": "固定班考勤1",
	"blockType": "1,2",
	"type": "1",
	"shiftIds": "",
	"changCycle": 30,
	"locationConfig": [
		{
			"locationName": "山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局",
			"longitude": "110.993466",
			"latitude": "35.008534",
			"radius": 100,
			"address": "山西省运城市盐湖区南城街道西城墙街日"
		}
	],
	"memberConfig": [
		{
			"assignType": "1",
			"relationId": "1945307819278397441"
		}
	],
	"workDayConfig": [
		{
			"weekDay": 1,
			"shiftId": "1946020398216839169"
		},
		{
			"weekDay": 1,
			"shiftId": "1946020398216839169"
		},
		{
			"weekDay": 1,
			"shiftId": "1946020398216839169"
		}
	],
	"replaceUserIds": [
		"1945656251901890561"
	
	]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| id | 1946123646491758593 | string | 是 | 考勤组ID |
| name | 固定班考勤1 | string | 是 | 考勤主名称 |
| blockType | 1,2 | string | 是 | 打卡方式  1GPS定位 2 拍照打卡 ，号分割 |
| type | 1 | string | 是 | 考勤类型 1固定班制 2排班制 |
| shiftIds | - | string | 否 | 考勤班次ID (排班制用) ,号分割 |
| changCycle | 30 | number | 否 | 换班周期（天）排班制用 |
| locationConfig | - | array | 否 | - |
| locationConfig.locationName | 山西省运城市盐湖区南城街道西城墙街日化分公司生活区电视局 | string | 是 | 位置名称 |
| locationConfig.longitude | 110.993466 | string | 是 | 经度 |
| locationConfig.latitude | 35.008534 | string | 是 | 纬度 |
| locationConfig.radius | 100 | number | 是 | 有效范围（米） |
| locationConfig.address | 山西省运城市盐湖区南城街道西城墙街日 | string | 是 | 打卡地址 |
| memberConfig | - | array | 否 | - |
| memberConfig.assignType | 1 | string | 否 | 分配类型 1部门分配 2 岗位分配  3人员分配 |
| memberConfig.relationId | 1945307819278397441 | string | 否 | 关联ID |
| workDayConfig | - | array | 否 | - |
| workDayConfig.weekDay | 1 | number | 是 | 工作日1-7 代表周一到周日 |
| workDayConfig.shiftId | 1946020398216839169 | string | 是 | 班次ID |
| replaceUserIds | - | array | 否 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除考勤组

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-28 10:22:14

> 更新时间: 2025-07-28 10:22:14

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceGroup/{ids}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| ids | - | string | 是 | 考勤组ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 员工打卡记录-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-06 09:29:35

> 更新时间: 2025-08-07 10:42:33

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceCheckRecord/list?pageSize=15&pageNume=1&groupId=&status=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | string | 是 | 每页条数 |
| pageNume | 1 | string | 是 | 页码 |
| groupId | - | string | 否 | 考勤组ID |
| beginDate | 2025-08-04 | string | 否 | 考勤日期 开始时间 |
| endDate | 2025-08-06 | string | 否 | 考勤日期 结束时间 |
| status | - | string | 否 | 状态 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 10,
    "rows": [
        {
            "id": "1952294670910410753",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-04",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-04 09:57:00",
            "checkType": "1",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "http://************:9000/file-xdc/image/2025/08/01/6219e811d5c7499c8144e0c734eece6c.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250806T093547Z&X-Amz-SignedHeaders=host&X-Amz-Credential=xdc%2F20250806%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Expires=120&X-Amz-Signature=bdd4e76bf449198a569140b87d6712e46854296f4b5a4c98e246832f54c9fca8,http://*************:8080/xdc_local/image/2025/08/01/525b7915cb604399b48c23f5a6ba6f8d.jpg",
            "remarks": null,
            "deviceType": "app",
            "status": "2",
            "createTime": "2025-08-04 17:04:56"
        },
        {
            "id": "1952294808244506625",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-04",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-04 11:57:00",
            "checkType": "2",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "3",
            "createTime": "2025-08-04 17:05:29"
        },
        {
            "id": "1952559264899813378",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-05",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-05 11:57:00",
            "checkType": "2",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "0",
            "createTime": "2025-08-05 10:36:20"
        },
        {
            "id": "1952885678139695105",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-05",
            "shiftName": "测试班次",
            "shiftTime": "09:01-18:00",
            "checkTime": "2025-08-05 11:57:00",
            "checkType": "1",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "2",
            "createTime": "2025-08-06 08:13:23"
        },
        {
            "id": "1952894584707108866",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-06",
            "shiftName": "测试班次",
            "shiftTime": "09:01-18:00",
            "checkTime": "2025-08-06 08:57:00",
            "checkType": "1",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "1",
            "createTime": "2025-08-06 08:48:46"
        },
        {
            "id": "1952899593335238658",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-06",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-06 08:57:00",
            "checkType": "1",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "http://************:9000/file-xdc/image/2025/08/01/6219e811d5c7499c8144e0c734eece6c.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250806T093547Z&X-Amz-SignedHeaders=host&X-Amz-Credential=xdc%2F20250806%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Expires=120&X-Amz-Signature=bdd4e76bf449198a569140b87d6712e46854296f4b5a4c98e246832f54c9fca8,http://*************:8080/xdc_local/image/2025/08/04/81199cde47c14a678f613ad99ec61a96.png",
            "remarks": null,
            "deviceType": "app",
            "status": "2",
            "createTime": "2025-08-06 09:08:40"
        },
        {
            "id": "1952933008189190146",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-06",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-06 11:57:00",
            "checkType": "2",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "3",
            "createTime": "2025-08-06 11:21:27"
        },
        {
            "id": "1952934871294832641",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-06",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-06 17:57:00",
            "checkType": "2",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "3",
            "createTime": "2025-08-06 11:28:51"
        },
        {
            "id": "1952935112567975938",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-06",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-06 16:57:00",
            "checkType": "1",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "2",
            "createTime": "2025-08-06 11:29:49"
        },
        {
            "id": "1953004332223946754",
            "groupName": "固定班",
            "userName": "沈勇",
            "attendanceDate": "2025-08-06",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "checkTime": "2025-08-06 18:57:00",
            "checkType": "2",
            "longitude": 112.551759,
            "latitude": 37.858121,
            "address": null,
            "pictureUrls": "",
            "remarks": null,
            "deviceType": "app",
            "status": "1",
            "createTime": "2025-08-06 16:04:52"
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 10 | number | - |
| rows | - | array | - |
| rows.id | 1952294670910410753 | string | - |
| rows.groupName | 固定班 | string | 考勤组名称 |
| rows.userName | 沈勇 | string | 员工姓名 |
| rows.attendanceDate | 2025-08-04 | string | 考勤日期 |
| rows.shiftName | 测试班次1 | string | 班次名称 |
| rows.shiftTime | 08:31-12:00 16:00-18:00 | string | 班次时间 |
| rows.checkTime | 2025-08-04 09:57:00 | string | 打卡时间 |
| rows.checkType | 1 | string | 打卡类型(1:上班 2:下班) |
| rows.longitude | 112.551759 | number | 经度 |
| rows.latitude | 37.858121 | number | 纬度 |
| rows.address | - | null | 定位地址 有定位打卡 |
| rows.pictureUrls | http://************:9000/file-xdc/image/2025/08/01/6219e811d5c7499c8144e0c734eece6c.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250806T093547Z&X-Amz-SignedHeaders=host&X-Amz-Credential=xdc%2F20250806%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Expires=120&X-Amz-Signature=bdd4e76bf449198a569140b87d6712e46854296f4b5a4c98e246832f54c9fca8,http://*************:8080/xdc_local/image/2025/08/01/525b7915cb604399b48c23f5a6ba6f8d.jpg | string | 打卡拍照 图片 |
| rows.remarks | - | null | 打卡备注 |
| rows.deviceType | app | string | 设备类型 |
| rows.status | 2 | string | 状态(0:无效 1:正常；2迟到；3早退；4旷工；5补卡；6严重迟到) |
| rows.createTime | 2025-08-04 17:04:56 | string | - |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 考勤记录-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 09:14:39

> 更新时间: 2025-08-07 09:54:49

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceResult/list?pageSize=15&pageNume=1&nickName=&groupId=&shiftId=&status=&deptId=&postId=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | number | 是 | 每页条数 |
| pageNume | 1 | string | 是 | 页码 |
| nickName | - | string | 否 | 员工姓名 |
| groupId | - | string | 否 | 考勤组ID |
| shiftId | - | string | 否 | 班次 |
| status | - | string | 否 | 状态 1:正常 2迟到；3早退；4旷工；5补卡；6严重迟到 |
| deptId | - | string | 否 | 部门ID |
| postId | - | string | 否 | 岗位 |
| beginDate | 2025-08-04 | string | 否 | 考勤日期 开始时间 |
| endDate | - | string | 否 | 考勤日期 结束时间 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 25,
    "rows": [
        {
            "id": "1952266904801849346",
            "tenantId": "475158",
            "nickName": "测试",
            "attendanceDate": "2025-08-04",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "测试,测试",
            "postName": "技术主管,研发主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952266904814432258",
            "tenantId": "475158",
            "nickName": "hsy",
            "attendanceDate": "2025-08-04",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "第一项目部",
            "postName": "技术主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952266904814432259",
            "tenantId": "475158",
            "nickName": "沈勇",
            "attendanceDate": "2025-08-04",
            "groupName": "固定班",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "workHours": "2.00",
            "deptName": "测试",
            "postName": "研发主管",
            "lateMinutes": 86,
            "earlyLeaveMinutes": 3,
            "status": "2"
        },
        {
            "id": "1952266904814432260",
            "tenantId": "475158",
            "nickName": "测试1",
            "attendanceDate": "2025-08-04",
            "groupName": "固定班",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "workHours": null,
            "deptName": "测试,测试",
            "postName": "研发主管,技术员",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952657026995781634",
            "tenantId": "475158",
            "nickName": "测试",
            "attendanceDate": "2025-08-05",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "技术部,技术部",
            "postName": "技术主管,研发主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952657027033530370",
            "tenantId": "475158",
            "nickName": "hsy",
            "attendanceDate": "2025-08-05",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "第一项目部",
            "postName": "技术主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952657027037724673",
            "tenantId": "475158",
            "nickName": "cby",
            "attendanceDate": "2025-08-05",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "技术部",
            "postName": "研发主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952657027041918977",
            "tenantId": "475158",
            "nickName": "whb",
            "attendanceDate": "2025-08-05",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "技术部,工程部,工程部",
            "postName": "技术主管,技术主管,研发主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952657027041918978",
            "tenantId": "475158",
            "nickName": "沈勇",
            "attendanceDate": "2025-08-05",
            "groupName": "固定班",
            "shiftName": "测试班次",
            "shiftTime": "09:01-18:00",
            "workHours": "0.00",
            "deptName": "工程部",
            "postName": "技术员",
            "lateMinutes": 176,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952657027054501890",
            "tenantId": "475158",
            "nickName": "测试1",
            "attendanceDate": "2025-08-05",
            "groupName": "固定班",
            "shiftName": "测试班次",
            "shiftTime": "09:01-18:00",
            "workHours": null,
            "deptName": "技术部,技术部",
            "postName": "研发主管,技术员",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "2"
        },
        {
            "id": "1952890842377097218",
            "tenantId": "475158",
            "nickName": "测试",
            "attendanceDate": "2025-08-06",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "技术部,技术部",
            "postName": "技术主管,研发主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "1"
        },
        {
            "id": "1952890842393874434",
            "tenantId": "475158",
            "nickName": "hsy",
            "attendanceDate": "2025-08-06",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "第一项目部",
            "postName": "技术主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "1"
        },
        {
            "id": "1952890842398068737",
            "tenantId": "475158",
            "nickName": "cby",
            "attendanceDate": "2025-08-06",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "技术部",
            "postName": "研发主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "1"
        },
        {
            "id": "1952890842402263042",
            "tenantId": "475158",
            "nickName": "whb",
            "attendanceDate": "2025-08-06",
            "groupName": "测试",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "workHours": null,
            "deptName": "技术部,工程部,工程部",
            "postName": "技术主管,技术主管,研发主管",
            "lateMinutes": 0,
            "earlyLeaveMinutes": 0,
            "status": "1"
        },
        {
            "id": "1952890842402263043",
            "tenantId": "475158",
            "nickName": "沈勇",
            "attendanceDate": "2025-08-06",
            "groupName": "固定班",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "workHours": "5.00",
            "deptName": "工程部",
            "postName": "技术员",
            "lateMinutes": 83,
            "earlyLeaveMinutes": 6,
            "status": "2,3"
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 25 | number | - |
| rows | - | array | - |
| rows.id | 1952266904801849346 | string | 考勤 |
| rows.tenantId | 475158 | string | - |
| rows.nickName | 测试 | string | 员工姓名 |
| rows.attendanceDate | 2025-08-04 | string | 考勤日期 |
| rows.groupName | 测试 | string | 考勤组名称 |
| rows.shiftName | 测试班次2 | string | 班次名称 合并 |
| rows.shiftTime | 20:00-01:00 | string | 班次时间 |
| rows.workHours | - | null | 工作工时 |
| rows.deptName | 测试,测试 | string | 部门 |
| rows.postName | 技术主管,研发主管 | string | 岗位 |
| rows.lateMinutes | 0 | number | 迟到分钟数 |
| rows.earlyLeaveMinutes | 0 | number | 早退分钟数 |
| rows.status | 2 | string | 状态(0:无效 1:正常；2迟到；3早退；4旷工；5补卡；6严重迟到) ,逗号分割 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 考勤记录打卡明细

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 10:41:42

> 更新时间: 2025-08-07 11:45:53

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceResult/checkInDetails/{resultId}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| resultId | 1953253461743124483 | string | 是 | 考勤记录ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "nickName": "测试1",
        "attendanceDate": "2025-08-07",
        "groupName": "测试2",
        "shiftName": "测试班次",
        "shiftTime": "09:01-18:00",
        "workHours": "0.00",
        "data": [
            {
                "date": "09:01",
                "checkTime": null,
                "status": "未打卡",
                "address": null,
                "pictureUrls": null,
                "remarks": null
            },
            {
                "date": "18:00",
                "checkTime": null,
                "status": "未打卡",
                "address": null,
                "pictureUrls": null,
                "remarks": null
            }
        ]
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.nickName | 测试1 | string | 员工姓名 |
| data.attendanceDate | 2025-08-07 | string | 考勤日期 |
| data.groupName | 测试2 | string | 考勤 |
| data.shiftName | 测试班次 | string | 班次 |
| data.shiftTime | 09:01-18:00 | string | 班次时间 |
| data.workHours | 0.00 | string | 工时 |
| data.data | - | array | 打卡记录 |
| data.data.date | 09:01 | string | 考勤时间 |
| data.data.checkTime | - | null | 打卡时间 |
| data.data.status | 未打卡 | string | 状态 1:正常 2迟到；3早退；4旷工；5补卡；6严重迟到 |
| data.data.address | - | null | 定位地址 |
| data.data.pictureUrls | - | null | 打卡图片 |
| data.data.remarks | - | null | 打卡备注 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 新增考勤补卡规则

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 14:39:53

> 更新时间: 2025-08-08 08:35:30

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceRepairConfig

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"name": "默认规则1",
	"canRepair": "1",
	"repairCount": 10,
	"repairDay": 1,
	"repairLimit": 1,
    "aGroupIds":["1949629302463074306"]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | 罗强 | string | 是 | 用户名 |
| canRepair | 1 | string | 是 | 允许补卡 0 否1是 |
| repairCount | 10 | number | 否 | 每月补卡次数 |
| repairDay | 1 | number | 否 | 每月补卡重新计算日 |
| repairLimit | 1 | number | 是 | 补卡时限 |
| aGroupIds | - | array | 否 | 考勤组ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 补卡规则列表-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 15:29:58

> 更新时间: 2025-08-07 15:40:10

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceRepairConfig/list?pageSize=15&pageNum=1&name=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | number | 是 | 每页条数 |
| pageNum | 1 | string | 是 | 页数 |
| name | - | string | 否 | 规则名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 1,
    "rows": [
        {
            "id": "1953354885999112193",
            "name": "默认规则",
            "canRepair": "1",
            "repairCount": 10,
            "repairDay": 1,
            "repairLimit": 1,
            "groupNames": "测试"
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.id | 1953354885999112193 | string | 考勤 |
| rows.name | 默认规则 | string | 规则名称 |
| rows.canRepair | 1 | string | 允许打卡 0 否1是 |
| rows.repairCount | 10 | number | 每月补卡次数 |
| rows.repairDay | 1 | number | 每月补卡重新计算日 |
| rows.repairLimit | 1 | number | 补卡时限 |
| rows.groupNames | 测试 | string | 应用考勤 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 补卡规则详情

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 15:47:56

> 更新时间: 2025-08-07 15:48:17

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceRepairConfig/{id}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| id | 1953354885999112193 | string | 是 | 规则ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": "1953354885999112193",
        "name": "默认规则",
        "canRepair": "1",
        "repairCount": 10,
        "repairDay": 1,
        "repairLimit": 1,
        "groupNames": "测试",
        "aGroupIds": [
            "1949629302463074306"
        ]
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 打卡记录 |
| data.id | 1953354885999112193 | string | 规则ID |
| data.name | 默认规则 | string | 规则名称 |
| data.canRepair | 1 | string | - |
| data.repairCount | 10 | number | - |
| data.repairDay | 1 | number | - |
| data.repairLimit | 1 | number | - |
| data.groupNames | 测试 | string | - |
| data.aGroupIds | - | array | 考勤组ID组 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改考勤补卡规则

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 15:50:50

> 更新时间: 2025-08-07 15:50:50

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceRepairConfig

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"id":"1953354885999112193",
	"name": "默认规则",
	"canRepair": "1",
	"repairCount": 10,
	"repairDay": 1,
	"repairLimit": 1,
    "aGroupIds":["1949629302463074306"]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| id | 1953354885999112193 | string | 是 | 规则ID |
| name | 默认规则 | string | 是 | 规则名称 |
| canRepair | 1 | string | 是 | 允许打卡 0 否1是 |
| repairCount | 10 | number | 否 | 每月补卡次数 |
| repairDay | 1 | number | 否 | 每月补卡重新计算日 |
| repairLimit | 1 | number | 是 | 补卡时限 |
| aGroupIds | - | array | 否 | 考勤组ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | null | 打卡记录 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除补卡规则

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 16:02:24

> 更新时间: 2025-08-07 16:02:24

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceRepairConfig/{ids}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| ids | 1953354885999112193 | string | 是 | 规则ID 组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 考勤调整

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-08 09:57:33

> 更新时间: 2025-08-08 14:50:38

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceResult/changeResult

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"resultId": "1952890842393874434",
	"reason": "后台调整"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| resultId | exercitation | string | 是 | 考勤记录ID |
| reason | nulla in incididunt proident | string | 否 | 调整备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 考勤变更记录-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-08 14:49:07

> 更新时间: 2025-08-08 14:54:15

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceChangeLog/list?pageSize=&pageNum=1&dept_id=&post_id=&shiftId=&groupId=&nickName=&changeType=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | - | number | 是 | - |
| pageNum | 1 | string | 是 | 页数 |
| dept_id | - | string | 否 | 部门ID |
| post_id | - | string | 否 | 岗位ID |
| beginDate | 2025-08-04 | string | 否 | 考勤日期 开始时间 |
| endDate | - | string | 否 | 考勤日期 结束时间 |
| shiftId | - | string | 否 | 班次 |
| groupId | - | string | 否 | 考勤组ID |
| nickName | - | string | 否 | 员工姓名 |
| changeType | - | string | 否 | 变更类型(1:补卡 2:管理员调整 ) |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 4,
    "rows": [
        {
            "id": "1953638387290226690",
            "resultId": "1952890842393874434",
            "userId": "1947946058292035585",
            "nickName": "hsy",
            "groupId": "1949629302463074306",
            "groupName": "测试",
            "shiftId": "1948184472975364098",
            "shiftName": "测试班次2",
            "shiftTime": "20:00-01:00",
            "deptIds": "1945307819177734145",
            "deptName": "第一项目部",
            "postIds": "1947822895516418049",
            "postName": "技术主管",
            "attendanceDate": "2025-08-06 08:00:00",
            "originalStatus": "迟到",
            "newStatus": "正常",
            "changeType": "2",
            "sourceId": null,
            "reason": "后台调整",
            "operatorId": "fengxin"
        },
        {
            "id": "1953659815472836610",
            "resultId": "1953253461743124483",
            "userId": "1947839522658160641",
            "nickName": "测试1",
            "groupId": "1952913479476244481",
            "groupName": "测试2",
            "shiftId": "1946020398216839169",
            "shiftName": "测试班次",
            "shiftTime": "09:01-18:00",
            "deptIds": "1951220450126213121,1951220450126213121",
            "deptName": "技术部,技术部",
            "postIds": "1947823414850945026,1951220929258336257",
            "postName": "研发主管,技术员",
            "attendanceDate": "2025-08-07 08:00:00",
            "originalStatus": "缺卡",
            "newStatus": "正常",
            "changeType": "2",
            "sourceId": null,
            "reason": "123",
            "operatorId": "fengxin"
        },
        {
            "id": "1953661129095294978",
            "resultId": "1953253461738930178",
            "userId": "1945656251901890561",
            "nickName": "沈勇",
            "groupId": "1949632906158776322",
            "groupName": "固定班",
            "shiftId": "1948183636429819905",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "deptIds": "1951220490001461250",
            "deptName": "工程部",
            "postIds": "1951220929258336257",
            "postName": "技术员",
            "attendanceDate": "2025-08-07 08:00:00",
            "originalStatus": "缺卡",
            "newStatus": "正常",
            "changeType": "2",
            "sourceId": null,
            "reason": "调整",
            "operatorId": "fengxin"
        },
        {
            "id": "1953707380491448322",
            "resultId": "1953253461743124482",
            "userId": "1952665777123373058",
            "nickName": "zjr",
            "groupId": "1949632906158776322",
            "groupName": "固定班",
            "shiftId": "1948183636429819905",
            "shiftName": "测试班次1",
            "shiftTime": "08:31-12:00 16:00-18:00",
            "deptIds": "1951220450126213121,1951220490001461250,1951220490001461250",
            "deptName": "技术部,工程部,工程部",
            "postIds": "1947823414850945026,1947822895516418049,1947823414850945026",
            "postName": "研发主管,技术主管,研发主管",
            "attendanceDate": "2025-08-07 08:00:00",
            "originalStatus": "缺卡",
            "newStatus": "正常",
            "changeType": "2",
            "sourceId": null,
            "reason": "",
            "operatorId": "fengxin"
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 4 | number | - |
| rows | - | array | - |
| rows.id | 1953638387290226690 | string | 考勤 |
| rows.resultId | 1952890842393874434 | string | 考勤记录ID |
| rows.userId | 1947946058292035585 | string | - |
| rows.nickName | hsy | string | 员工姓名 |
| rows.groupId | 1949629302463074306 | string | 考勤组ID |
| rows.groupName | 测试 | string | 考勤组名称 |
| rows.shiftId | 1948184472975364098 | string | 班次 |
| rows.shiftName | 测试班次2 | string | 班次名称 合并 |
| rows.shiftTime | 20:00-01:00 | string | 班次时间 |
| rows.deptIds | 1945307819177734145 | string | - |
| rows.deptName | 第一项目部 | string | 部门 |
| rows.postIds | 1947822895516418049 | string | - |
| rows.postName | 技术主管 | string | 岗位 |
| rows.attendanceDate | 2025-08-06 08:00:00 | string | 考勤日期 |
| rows.originalStatus | 迟到 | string | 原状态 |
| rows.newStatus | 正常 | string | 新状态 |
| rows.changeType | 2 | string | 变更类型(1:补卡 2:管理员调整 ) |
| rows.sourceId | - | null | - |
| rows.reason | 后台调整 | string | 变更原因 |
| rows.operatorId | fengxin | string | 操作人 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 批量删除考勤变更记录

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-08 14:55:24

> 更新时间: 2025-08-08 14:55:32

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceChangeLog/{ids}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| ids | - | string | 是 | 记录ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 清空考勤变更记录

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-08 14:56:20

> 更新时间: 2025-08-08 14:56:20

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/attendanceChangeLog/clean

**请求方式**

> DELETE

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 项目管理

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 15:16:14

> 更新时间: 2025-07-16 15:16:14

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 新增工点

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 15:18:27

> 更新时间: 2025-07-16 15:23:14

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/workPoint

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"pointName": "隧道南段1",
	"pointCode": "sd-0011",
	"pointSort": 85,
	"remark": "ex Duis"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pointName | 隧道南段 | string | 是 | 工点名称 |
| pointCode | sd-001 | string | 否 | 工点编号 |
| pointSort | 85 | number | 否 | 显示顺序 |
| remark | ex Duis | string | 否 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工点列表-分页

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 15:26:12

> 更新时间: 2025-07-16 15:26:12

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/workPoint/list?pageNum=1&pageSize=15&pointName=&pointCode=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 15 | string | 是 | 每页条数 |
| pointName | - | string | 否 | 工点名称 |
| pointCode | - | string | 否 | 工点编号 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "total": 2,
    "rows": [
        {
            "pointId": "1945383694397095937",
            "pointName": "隧道南段1",
            "pointCode": "sd-0011",
            "pointSort": 85,
            "remark": "ex Duis",
            "createTime": null
        },
        {
            "pointId": "1945383046872064002",
            "pointName": "隧道南段",
            "pointCode": "sd-001",
            "pointSort": 85,
            "remark": "ex Duis",
            "createTime": null
        }
    ],
    "code": 200,
    "msg": "查询成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.pointId | 1945383694397095937 | string | - |
| rows.pointName | 隧道南段1 | string | 工点名称 |
| rows.pointCode | sd-0011 | string | 工点编号 |
| rows.pointSort | 85 | number | 显示顺序 降序 |
| rows.remark | ex Duis | string | 备注 |
| rows.createTime | - | null | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工点详情

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 15:28:13

> 更新时间: 2025-07-16 15:28:13

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/workPoint/{pointId}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pointId | 1945383046872064002 | string | 是 | 工点ID |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "pointId": "1945383046872064002",
        "pointName": "隧道南段",
        "pointCode": "sd-001",
        "pointSort": 85,
        "remark": "ex Duis",
        "createTime": null
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.pointId | 1945383046872064002 | string | 工点ID |
| data.pointName | 隧道南段 | string | 工点名称 |
| data.pointCode | sd-001 | string | 工点编号 |
| data.pointSort | 85 | number | 显示顺序 降序 |
| data.remark | ex Duis | string | 备注 |
| data.createTime | - | null | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改工点

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 15:31:52

> 更新时间: 2025-07-18 14:55:21

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/workPoint

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"pointId": "1945383046872064002",
	"pointName": "一号工点",
	"pointCode": "GDBH001",
	"pointSort": 1,
	"remark": "该工点为重点施工区域"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pointId | 1945383046872064002 | string | 是 | 工点ID |
| pointName | 一号工点 | string | 是 | 工点名称 |
| pointCode | "GDBH001" | string | 否 | 工点编号 |
| pointSort | 1 | number | 否 | 显示顺序 降序 |
| remark | "该工点为重点施工区域" | string | 否 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | null | 返回数据 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除工点

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 15:34:10

> 更新时间: 2025-07-16 15:34:29

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/workPoint/{pointIds}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pointIds | 1945383694397095937 | string | 是 | 工点ID组 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 组新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 10:36:58

> 更新时间: 2025-07-23 10:36:58

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/group

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "groupName": "测试组2",
  "groupSort": 0,
  "remark":""
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | 测试组 | string | 是 | 组名称 |
| groupSort | 0 | number | 否 | 显示顺序 |
| remark | - | string | 否 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 组列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 10:39:36

> 更新时间: 2025-07-23 10:39:36

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/group/list?groupName=&pageNum=1&pageSize=10

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | - | string | 否 | 组名称 |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":4,"rows":[{"groupId":"1947826548543401986","groupName":"现场组","groupSort":0,"remark":"","createTime":"2025-07-15 11:54:12"},{"groupId":"1947826548543401987","groupName":"内业组","groupSort":0,"remark":null,"createTime":"2025-07-23 08:38:54"},{"groupId":"1947826548543401988","groupName":"检测组","groupSort":0,"remark":null,"createTime":"2025-07-23 08:39:11"},{"groupId":"1947848296261316609","groupName":"测试组2","groupSort":0,"remark":"","createTime":"2025-07-23 10:36:38"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 4 | number | - |
| rows | - | array | - |
| rows.groupId | 1947826548543401986 | string | 组id |
| rows.groupName | 现场组 | string | 组名称 |
| rows.groupSort | 0 | number | 显示顺序 |
| rows.remark | - | string | 备注 |
| rows.createTime | 2025-07-15 11:54:12 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 组列表（搜索列表）

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 10:40:58

> 更新时间: 2025-07-23 10:41:07

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/group/select?groupName=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | - | string | 否 | 组名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"groupId":"1947826548543401986","groupName":"现场组","groupSort":0,"remark":"","createTime":"2025-07-15 11:54:12"},{"groupId":"1947826548543401987","groupName":"内业组","groupSort":0,"remark":null,"createTime":"2025-07-23 08:38:54"},{"groupId":"1947826548543401988","groupName":"检测组","groupSort":0,"remark":null,"createTime":"2025-07-23 08:39:11"},{"groupId":"1947848296261316609","groupName":"测试组2","groupSort":0,"remark":"","createTime":"2025-07-23 10:36:38"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.groupId | 1947826548543401986 | string | 组id |
| data.groupName | 现场组 | string | 组名称 |
| data.groupSort | 0 | number | 显示顺序 |
| data.remark | - | string | 备注 |
| data.createTime | 2025-07-15 11:54:12 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取组详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 10:42:35

> 更新时间: 2025-07-23 10:42:35

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/group/1947848296261316609

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"groupId":"1947848296261316609","groupName":"测试组2","groupSort":0,"remark":"","createTime":"2025-07-23 10:36:38"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.groupId | 1947848296261316609 | string | 组id |
| data.groupName | 测试组2 | string | 组名称 |
| data.groupSort | 0 | number | 显示顺序 |
| data.remark | - | string | 备注 |
| data.createTime | 2025-07-23 10:36:38 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改组

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 10:43:57

> 更新时间: 2025-07-23 10:43:57

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/group

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "groupId": "1947848296261316609",
  "groupName": "测试组1",
  "groupSort": 0,
  "remark":""
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 组删除 

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 10:44:38

> 更新时间: 2025-07-23 10:44:38

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/group/1947848296261316609

**请求方式**

> DELETE

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工序分类新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 17:17:53

> 更新时间: 2025-07-23 17:19:50

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCate

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "cateName": "路基施工"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | 测试组2 | string | 是 | 工序分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工序分类列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 17:21:54

> 更新时间: 2025-07-23 17:21:54

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCate/list?cateName=&pageNum=1&pageSize=10

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | - | string | 否 | 工序分类名称 |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":3,"rows":[{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"},{"stepCateId":"1945011846935228417","cateName":"隧道施工","createTime":"2025-07-15 14:45:36"},{"stepCateId":"1945011560883695618","cateName":"桥梁施工","createTime":"2025-07-15 14:44:27"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 3 | number | - |
| rows | - | array | - |
| rows.stepCateId | 1945011893089349633 | string | 分类 |
| rows.cateName | 路基施工 | string | 工序分类名称 |
| rows.createTime | 2025-07-15 14:45:47 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工序分类列表(搜索列表)

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-16 09:48:53

> 更新时间: 2025-07-23 17:23:27

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCate/listAll?cateName=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | - | string | 否 | 工序分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"},{"stepCateId":"1945011846935228417","cateName":"隧道施工","createTime":"2025-07-15 14:45:36"},{"stepCateId":"1945011560883695618","cateName":"桥梁施工","createTime":"2025-07-15 14:44:27"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.stepCateId | 1945011893089349633 | string | 工序分类id |
| data.cateName | 路基施工 | string | 工序分类名称 |
| data.createTime | 2025-07-15 14:45:47 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取工序分类详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 14:31:26

> 更新时间: 2025-07-23 17:25:53

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCate/1947949747534778370

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.stepCateId | 1945011893089349633 | string | 工序分类id |
| data.cateName | 路基施工 | string | 工序分类名称 |
| data.createTime | 2025-07-15 14:45:47 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改工序分类

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 14:33:37

> 更新时间: 2025-07-23 17:27:06

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCate

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "stepCateId": "1947949747534778370",
  "cateName": "桥梁施工"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序分类删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 14:35:01

> 更新时间: 2025-07-23 17:31:04

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCate/1947949747534778370

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 09:04:37

> 更新时间: 2025-07-25 09:04:37

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStep

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "stepName": "洞身开挖1",
  "stepCode":"",
  "stepAlias":"",
  "stepBefore":0,
  "stepSort":0,
  "cateIds":[]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| stepName | 路基施工 | string | 是 | 工序名称 |
| stepCode | - | string | 否 | 工序编号 |
| stepAlias | - | string | 否 | 工序别名 |
| stepBefore | - | string | 否 | 前置工序 |
| stepSort | 0 | number | 否 | 显示顺序 |
| cateIds | - | array | 否 | 所属分类 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 09:15:39

> 更新时间: 2025-07-25 09:15:39

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStep/list?pageNum=1&pageSize=10&stepName=&stepCode=&stepAlias=&params[cateId]=&stepBefore=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |
| stepName | - | string | 否 | 工序名称 |
| stepCode | - | string | 否 | 工序编号 |
| stepAlias | - | string | 否 | 工序别名 |
| params[cateId] | - | string | 否 | 所属分类 |
| stepBefore | - | string | 否 | 前置工序 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":3,"rows":[{"stepId":"1947819580269375489","stepName":"二衬","stepCode":null,"stepAlias":null,"stepBefore":"1947819350790615042","stepBeforeName":"仰拱","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:42:31"},{"stepId":"1947819350790615042","stepName":"仰拱","stepCode":null,"stepAlias":null,"stepBefore":"1947819213892726786","stepBeforeName":"初支","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:37"},{"stepId":"1947819213892726786","stepName":"初支","stepCode":null,"stepAlias":null,"stepBefore":0,"stepBeforeName":null,"stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:04"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 3 | number | - |
| rows | - | array | - |
| rows.stepId | 1947819580269375489 | string | - |
| rows.stepName | 二衬 | string | 工序名称 |
| rows.stepCode | - | null | 工序编号 |
| rows.stepAlias | - | null | 工序别名 |
| rows.stepBefore | 1947819350790615042 | string | 前置工序 |
| rows.stepBeforeName | 仰拱 | string | 前置工序 |
| rows.stepSort | 0 | number | 显示顺序 |
| rows.cateIds | 1945011846935228417 | string | 分类 |
| rows.cateNames | 隧道施工 | string | 所属分类 |
| rows.createTime | 2025-07-23 08:42:31 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序列表(搜索列表) 

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 09:19:37

> 更新时间: 2025-07-25 09:19:37

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStep/listAll?stepName=&stepCode=&stepAlias=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| stepName | - | string | 否 | 工序名称 |
| stepCode | - | string | 否 | 工序编号 |
| stepAlias | - | string | 否 | 工序别名 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepId":"1947819580269375489","stepName":"二衬","stepCode":null,"stepAlias":null,"stepBefore":"1947819350790615042","stepBeforeName":"仰拱","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:42:31"},{"stepId":"1947819350790615042","stepName":"仰拱","stepCode":null,"stepAlias":null,"stepBefore":"1947819213892726786","stepBeforeName":"初支","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:37"},{"stepId":"1947819213892726786","stepName":"初支","stepCode":null,"stepAlias":null,"stepBefore":0,"stepBeforeName":null,"stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:04"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.stepId | 1947819580269375489 | string | - |
| data.stepName | 二衬 | string | 工序名称 |
| data.stepCode | - | null | 工序编号 |
| data.stepAlias | - | null | 工序别名 |
| data.stepBefore | 1947819350790615042 | string | 前置工序 |
| data.stepBeforeName | 仰拱 | string | 前置工序 |
| data.stepSort | 0 | number | - |
| data.cateIds | 1945011846935228417 | string | - |
| data.cateNames | 隧道施工 | string | 所属分类名称 |
| data.createTime | 2025-07-23 08:42:31 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取工序详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 09:22:51

> 更新时间: 2025-07-25 09:22:51

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStep/1948549819987447810

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"stepId":"1947819580269375489","stepName":"二衬","stepCode":null,"stepAlias":null,"stepBefore":"1947819350790615042","stepBeforeName":"仰拱","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:42:31"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.stepId | 1947819580269375489 | string | - |
| data.stepName | 二衬 | string | 工序名称 |
| data.stepCode | - | null | 工序编号 |
| data.stepAlias | - | null | 工序别名 |
| data.stepBefore | 1947819350790615042 | string | 前置工序 |
| data.stepBeforeName | 仰拱 | string | 前置工序 |
| data.stepSort | 0 | number | 显示顺序 |
| data.cateIds | 1945011846935228417 | string | 分类ids |
| data.cateNames | 隧道施工 | string | 所属分类名称 |
| data.createTime | 2025-07-23 08:42:31 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改工序

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 09:24:07

> 更新时间: 2025-07-25 09:24:07

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStep

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "stepId": "1948549819987447810",
  "stepName": "洞身开挖1",
  "stepCode":"",
  "stepAlias":"",
  "stepBefore":0,
  "stepSort":0,
  "cateIds":[]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 09:27:23

> 更新时间: 2025-07-25 09:27:23

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStep/1948549819987447810

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:14:45

> 更新时间: 2025-07-25 11:14:45

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCate

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "name": "超前及地质不良作业线"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | 测试组2 | string | 是 | 工序分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:15:34

> 更新时间: 2025-07-25 11:15:34

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCate/list?name=&pageNum=1&pageSize=10

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | - | string | 否 | 清单分类名称 |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":1,"rows":[{"id":"1947224540304121858","name":"超前及地质不良作业线","createTime":"2025-07-21 17:18:03"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.id | 1947224540304121858 | string | - |
| rows.name | 超前及地质不良作业线 | string | 清单分类名称 |
| rows.createTime | 2025-07-21 17:18:03 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类列表(搜索列表) 

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:19:13

> 更新时间: 2025-07-25 11:19:13

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCate/listAll?name=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | - | string | 否 | 清单分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"},{"stepCateId":"1945011846935228417","cateName":"隧道施工","createTime":"2025-07-15 14:45:36"},{"stepCateId":"1945011560883695618","cateName":"桥梁施工","createTime":"2025-07-15 14:44:27"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.stepCateId | 1945011893089349633 | string | 工序分类id |
| data.cateName | 路基施工 | string | 工序分类名称 |
| data.createTime | 2025-07-15 14:45:47 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取清单分类详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:19:57

> 更新时间: 2025-07-25 11:19:57

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCate/1948582650625196034

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"id":"1947224540304121858","name":"超前及地质不良作业线","createTime":"2025-07-21 17:18:03"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.id | 1947224540304121858 | string | - |
| data.name | 超前及地质不良作业线 | string | 清单分类名称 |
| data.createTime | 2025-07-21 17:18:03 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改清单分类

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:20:32

> 更新时间: 2025-07-25 11:20:32

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCate

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "id": "1948582650625196034",
  "name": "超前及地质不良作业线"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:21:26

> 更新时间: 2025-07-25 11:21:26

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCate/1948582650625196034

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:29:55

> 更新时间: 2025-07-25 14:12:46

```text
暂无描述
```

**接口状态**

> 需修改

**接口URL**

> /system/inventory

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "inventoryName": "超前水平钻孔99",
  "cateId":1947230213406466049,
  "beforeInventory":0,
  "remark":"",
  "status":"0",
  "stepList":[
    {
      "stepId":1945037202924871682,
      "sort":0
    }
  ],
  "relationList":[
    {
      "assignType":"1",
      "relationId":1944667500893503489
    },
    {
      "assignType":"1",
      "relationId":1944598154489892866
    },
    {
      "assignType":"2",
      "relationId":1944678525604741122
    }
  ],
  "groupList":[
    {
      "groupId":"1944968716408115201",
      "score":5,
      "formInfo":
      {
        "rulesJson":[{"type":"stepper","field":"Fmoamd723ppjaec","title":"搭接进度","info":"","$required":false,"_fc_id":"id_Fpylmd723ppjafc","name":"ref_Fohkmd723ppjagc","display":true,"hidden":false,"_fc_drag_tag":"stepper","value":1},{"type":"switch","field":"Fxcpmd724lg8ahc","title":"孔数是否符合设计","info":"","$required":false,"props":{"activeValue":true,"inactiveValue":false},"_fc_id":"id_F4ugmd724lg8aic","name":"ref_Fr01md724lg8ajc","display":true,"hidden":false,"_fc_drag_tag":"switch"}],
        "optionsJson":{"language":{"zh-cn":{"Az87OmQS":"商品名称","BAVvUidu":"商品价格","CkD1fG2H":"商品描述","DgH2iJ3K":"库存数量","EhI3jK4L":"发货方式","FiJ4kL5M":"配送时间","GjK5lM6N":"用户评价","HkL6mN7O":"添加到购物车","IkM7nO8P":"立即购买","JlN8oP9Q":"优惠活动","KmO9pQ0R":"搜索商品","LnP0qR1S":"分类","MoQ1rS2T":"品牌","NpR2sT3U":"付款方式","OqS3tU4V":"订单确认","PrT4uV5W":"用户注册","QsU5vW6X":"用户登录","RtV6wX7Y":"联系客服","SuW7xY8Z":"退出登录","TvX8yZ9A":"个人信息","UwY9zA0B":"购物车","VxZ0aB1C":"结算","WyA1bC2D":"运费","XzB2cD3E":"订单状态","YaC3dE4F":"支付成功","ZbD4eF5G":"支付失败"},"en":{"Az87OmQS":"Goods name","BAVvUidu":"Goods price","CkD1fG2H":"Product description","DgH2iJ3K":"Stock quantity","EhI3jK4L":"Shipping method","FiJ4kL5M":"Delivery time","GjK5lM6N":"User reviews","HkL6mN7O":"Add to cart","IkM7nO8P":"Buy now","JlN8oP9Q":"Promotions","KmO9pQ0R":"Search products","LnP0qR1S":"Category","MoQ1rS2T":"Brand","NpR2sT3U":"Payment method","OqS3tU4V":"Order confirmation","PrT4uV5W":"User registration","QsU5vW6X":"User login","RtV6wX7Y":"Contact customer service","SuW7xY8Z":"Logout","TvX8yZ9A":"Personal information","UwY9zA0B":"Shopping cart","VxZ0aB1C":"Checkout","WyA1bC2D":"Shipping fee","XzB2cD3E":"Order status","YaC3dE4F":"Payment successful","ZbD4eF5G":"Payment failed"}},"form":{"labelWidth":"6.2em","colon":false},"resetBtn":{"show":true,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"超前水平钻孔"}
      }
    }
  ]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| inventoryName | 超前水平钻孔99 | string | 是 | 清单名称 |
| cateId | 1947230213406466000 | number | 是 | 所属分类 |
| beforeInventory | 0 | number | 是 | 前置清单 |
| remark | - | string | 是 | 备注 |
| status | 0 | string | 是 | 状态 0 草稿 1正常 2停用 |
| stepList | - | array | 是 | 所属工序 |
| stepList.stepId | 1945037202924871700 | number | 是 | 工序id |
| stepList.sort | 0 | number | 是 | 顺序 |
| relationList | - | array | 是 | 权限关联 |
| relationList.assignType | 1 | string | 是 | 分配类型 1部门分配 2 岗位分配 |
| relationList.relationId | 1944667500893503500 | number | 是 | 关联id |
| groupList | - | array | 是 | 所属组 |
| groupList.groupId | 1944968716408115201 | string | 是 | 组id |
| groupList.score | 5 | number | 是 | 分数 |
| groupList.formInfo | - | object | 是 | 表单 |
| groupList.formInfo.rulesJson | - | array | 是 | 表单的规则和字段的整体配置数据 |
| groupList.formInfo.rulesJson.type | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.field | Fmoamd723ppjaec | string | 是 | - |
| groupList.formInfo.rulesJson.title | 搭接进度 | string | 是 | - |
| groupList.formInfo.rulesJson.info | - | string | 是 | - |
| groupList.formInfo.rulesJson.$required | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_id | id_Fpylmd723ppjafc | string | 是 | - |
| groupList.formInfo.rulesJson.name | ref_Fohkmd723ppjagc | string | 是 | - |
| groupList.formInfo.rulesJson.display | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.hidden | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_drag_tag | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.value | 1 | number | 是 | - |
| groupList.formInfo.rulesJson.props | - | object | 是 | - |
| groupList.formInfo.rulesJson.props.activeValue | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.props.inactiveValue | false | boolean | 是 | - |
| groupList.formInfo.optionsJson | - | object | 是 | 表单的配置数据 |
| groupList.formInfo.optionsJson.language | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.Az87OmQS | 商品名称 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.BAVvUidu | 商品价格 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.CkD1fG2H | 商品描述 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.DgH2iJ3K | 库存数量 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.EhI3jK4L | 发货方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.FiJ4kL5M | 配送时间 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.GjK5lM6N | 用户评价 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.HkL6mN7O | 添加到购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.IkM7nO8P | 立即购买 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.JlN8oP9Q | 优惠活动 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.KmO9pQ0R | 搜索商品 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.LnP0qR1S | 分类 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.MoQ1rS2T | 品牌 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.NpR2sT3U | 付款方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.OqS3tU4V | 订单确认 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.PrT4uV5W | 用户注册 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.QsU5vW6X | 用户登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.RtV6wX7Y | 联系客服 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.SuW7xY8Z | 退出登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.TvX8yZ9A | 个人信息 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.UwY9zA0B | 购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.VxZ0aB1C | 结算 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.WyA1bC2D | 运费 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.XzB2cD3E | 订单状态 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.YaC3dE4F | 支付成功 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.ZbD4eF5G | 支付失败 | string | 是 | - |
| groupList.formInfo.optionsJson.language.en | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.en.Az87OmQS | Goods name | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.BAVvUidu | Goods price | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.CkD1fG2H | Product description | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.DgH2iJ3K | Stock quantity | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.EhI3jK4L | Shipping method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.FiJ4kL5M | Delivery time | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.GjK5lM6N | User reviews | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.HkL6mN7O | Add to cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.IkM7nO8P | Buy now | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.JlN8oP9Q | Promotions | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.KmO9pQ0R | Search products | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.LnP0qR1S | Category | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.MoQ1rS2T | Brand | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.NpR2sT3U | Payment method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.OqS3tU4V | Order confirmation | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.PrT4uV5W | User registration | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.QsU5vW6X | User login | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.RtV6wX7Y | Contact customer service | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.SuW7xY8Z | Logout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.TvX8yZ9A | Personal information | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.UwY9zA0B | Shopping cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.VxZ0aB1C | Checkout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.WyA1bC2D | Shipping fee | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.XzB2cD3E | Order status | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.YaC3dE4F | Payment successful | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.ZbD4eF5G | Payment failed | string | 是 | - |
| groupList.formInfo.optionsJson.form | - | object | 是 | - |
| groupList.formInfo.optionsJson.form.labelWidth | 6.2em | string | 是 | - |
| groupList.formInfo.optionsJson.form.colon | false | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.innerText | 重置 | string | 是 | - |
| groupList.formInfo.optionsJson.submitBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.innerText | 提交 | string | 是 | - |
| groupList.formInfo.optionsJson.formName | 超前水平钻孔 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:30:40

> 更新时间: 2025-07-25 11:30:40

```text
暂无描述
```

**接口状态**

> 需修改

**接口URL**

> /system/inventory/list?pageNum=1&pageSize=100000&inventoryName=&beforeInventory=&status=&params[stepId]=&params[groupId]=&params[deptId]=&params[postId]=&cateId=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 100000 | string | 是 | 条数 |
| inventoryName | - | string | 否 | 清单名称 |
| beforeInventory | - | string | 否 | 前置清单 |
| status | - | string | 否 | 状态 0 草稿 1正常 2停用 |
| params[stepId] | - | string | 否 | 所属工序 |
| params[groupId] | - | string | 否 | 所属组 |
| params[deptId] | - | string | 否 | 所属部门 |
| params[postId] | - | string | 否 | 所属岗位 |
| cateId | - | string | 否 | 所属分类 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":8,"rows":[{"inventoryId":"1947554121854836738","cateId":0,"cateName":"","inventoryName":"超前水平钻孔999","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-22 15:07:41"},{"inventoryId":"1947457252814315521","cateId":"1947230213406466049","cateName":"超前及地质不良作业线","inventoryName":"超前水平钻孔99","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-22 08:42:46"},{"inventoryId":"1946056066395553793","cateId":0,"cateName":"","inventoryName":"超前水平钻孔6","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:54:57"},{"inventoryId":"1946055074157441025","cateId":0,"cateName":"","inventoryName":"超前水平钻孔4","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:51:00"},{"inventoryId":"1946050391804334082","cateId":0,"cateName":"","inventoryName":"超前水平钻孔3","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:32:24"},{"inventoryId":"1945775059629109250","cateId":0,"cateName":"","inventoryName":"超前水平钻孔2","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 17:18:20"},{"inventoryId":"1945770211009490946","cateId":0,"cateName":"","inventoryName":"超前水平钻孔1","beforeInventory":"1945759589827887105","beforeInventoryName":"超前水平钻孔","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 16:59:04"},{"inventoryId":"1945759589827887105","cateId":0,"cateName":"","inventoryName":"超前水平钻孔","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 16:16:51"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 8 | number | - |
| rows | - | array | - |
| rows.inventoryId | 1947554121854836738 | string | 清单id |
| rows.cateId | 0 | number | 所属分类 |
| rows.cateName | - | string | 所属分类名称 |
| rows.inventoryName | 超前水平钻孔999 | string | 清单名称 |
| rows.beforeInventory | 0 | number | 前置清单 |
| rows.beforeInventoryName | - | string | 前置清单名称 |
| rows.remark | - | string | 备注 |
| rows.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| rows.stepNames | 超前及地质不良作业线 | string | 所属工序 |
| rows.deptNames | 技术部,工程管理 | string | 所属部门 |
| rows.postNames | 技术员 | string | 所属 |
| rows.groupNames | 现场组 | string | 所属 |
| rows.groupList | - | null | - |
| rows.stepList | - | null | - |
| rows.relationList | - | null | - |
| rows.createTime | 2025-07-22 15:07:41 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取清单信息（编辑回显）

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:31:07

> 更新时间: 2025-07-25 11:31:07

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventory/1947822446988038146

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"inventoryId":"1947457252814315521","cateId":"1947230213406466049","cateName":null,"inventoryName":"超前水平钻孔99","beforeInventory":0,"beforeInventoryName":null,"remark":"","status":"0","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":[{"inventoryId":"1947457252814315521","groupId":"1944968716408115201","score":5,"formInfo":{"formId":"1947457253804171265","inventoryId":"1947457252814315521","groupId":"1944968716408115201","title":"超前水平钻孔","description":null,"rulesJson":[],"optionsJson":{}}}],"stepList":[{"inventoryId":"1947457252814315521","stepId":"1945037202924871682","sort":0}],"relationList":[{"id":"1947457253451849730","inventoryId":"1947457252814315521","assignType":"1","relationId":"1944598154489892866"},{"id":"1947457253426683906","inventoryId":"1947457252814315521","assignType":"1","relationId":"1944667500893503489"},{"id":"1947457253460238337","inventoryId":"1947457252814315521","assignType":"2","relationId":"1944678525604741122"}],"createTime":"2025-07-22 08:42:46"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.inventoryId | 1947457252814315521 | string | 清单id |
| data.cateId | 1947230213406466049 | string | 所属分类 |
| data.cateName | - | null | - |
| data.inventoryName | 超前水平钻孔99 | string | 清单名称 |
| data.beforeInventory | 0 | number | 前置 |
| data.beforeInventoryName | - | null | 前置清单名称 |
| data.remark | - | string | 备注 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.stepNames | - | null | - |
| data.deptNames | - | null | - |
| data.postNames | - | null | - |
| data.groupNames | - | null | - |
| data.groupList | - | array | 所属 |
| data.groupList.inventoryId | 1947457252814315521 | string | 清单id |
| data.groupList.groupId | 1944968716408115201 | string | 组id |
| data.groupList.score | 5 | number | 分数 |
| data.groupList.formInfo | - | object | 表单相关 |
| data.groupList.formInfo.formId | 1947457253804171265 | string | 表单id |
| data.groupList.formInfo.inventoryId | 1947457252814315521 | string | 清单id |
| data.groupList.formInfo.groupId | 1944968716408115201 | string | 组id |
| data.groupList.formInfo.title | 超前水平钻孔 | string | 表单 |
| data.groupList.formInfo.description | - | null | 表单描述 |
| data.groupList.formInfo.rulesJson | - | array | 表单的规则和字段的整体配置数据，通常包含多个字段的配置 |
| data.groupList.formInfo.optionsJson | - | object | 表单的配置数据（例如：布局、尺寸、全局数据等） |
| data.stepList | - | array | 所属工序 |
| data.stepList.inventoryId | 1947457252814315521 | string | 清单id |
| data.stepList.stepId | 1945037202924871682 | string | 工序id |
| data.stepList.sort | 0 | number | 清单显示 |
| data.relationList | - | array | 权限配置 |
| data.relationList.id | 1947457253451849730 | string |  |
| data.relationList.inventoryId | 1947457252814315521 | string | 清单id |
| data.relationList.assignType | 1 | string | 分配类型 1部门分配 2 岗位分配 |
| data.relationList.relationId | 1944598154489892866 | string | 关联id |
| data.createTime | 2025-07-22 08:42:46 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改清单

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:31:53

> 更新时间: 2025-07-25 11:31:53

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventory

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "inventoryId":1946056066395553793,
  "inventoryName": "超前水平钻孔6",
  "cateId":1947230213406466049,
  "beforeInventory":0,
  "remark":"",
  "status":"0",
  "isEnable":"0",
  "stepList":[
    {
      "stepId":1945037202924871682,
      "sort":0
    }
  ],
  "relationList":[
    {
      "assignType":"1",
      "relationId":1944667500893503489
    },
    {
      "assignType":"1",
      "relationId":1944598154489892866
    },
    {
      "assignType":"2",
      "relationId":1944678525604741122
    }
  ],
  "groupList":[
    {
      "groupId":"1944968716408115201",
      "score":5,
      "formInfo":
      {
        "rulesJson":[{"type":"stepper","field":"Fmoamd723ppjaec","title":"搭接进度","info":"","$required":false,"_fc_id":"id_Fpylmd723ppjafc","name":"ref_Fohkmd723ppjagc","display":true,"hidden":false,"_fc_drag_tag":"stepper","value":1},{"type":"switch","field":"Fxcpmd724lg8ahc","title":"孔数是否符合设计","info":"","$required":false,"props":{"activeValue":true,"inactiveValue":false},"_fc_id":"id_F4ugmd724lg8aic","name":"ref_Fr01md724lg8ajc","display":true,"hidden":false,"_fc_drag_tag":"switch"}],
        "optionsJson":{"language":{"zh-cn":{"Az87OmQS":"商品名称","BAVvUidu":"商品价格","CkD1fG2H":"商品描述","DgH2iJ3K":"库存数量","EhI3jK4L":"发货方式","FiJ4kL5M":"配送时间","GjK5lM6N":"用户评价","HkL6mN7O":"添加到购物车","IkM7nO8P":"立即购买","JlN8oP9Q":"优惠活动","KmO9pQ0R":"搜索商品","LnP0qR1S":"分类","MoQ1rS2T":"品牌","NpR2sT3U":"付款方式","OqS3tU4V":"订单确认","PrT4uV5W":"用户注册","QsU5vW6X":"用户登录","RtV6wX7Y":"联系客服","SuW7xY8Z":"退出登录","TvX8yZ9A":"个人信息","UwY9zA0B":"购物车","VxZ0aB1C":"结算","WyA1bC2D":"运费","XzB2cD3E":"订单状态","YaC3dE4F":"支付成功","ZbD4eF5G":"支付失败"},"en":{"Az87OmQS":"Goods name","BAVvUidu":"Goods price","CkD1fG2H":"Product description","DgH2iJ3K":"Stock quantity","EhI3jK4L":"Shipping method","FiJ4kL5M":"Delivery time","GjK5lM6N":"User reviews","HkL6mN7O":"Add to cart","IkM7nO8P":"Buy now","JlN8oP9Q":"Promotions","KmO9pQ0R":"Search products","LnP0qR1S":"Category","MoQ1rS2T":"Brand","NpR2sT3U":"Payment method","OqS3tU4V":"Order confirmation","PrT4uV5W":"User registration","QsU5vW6X":"User login","RtV6wX7Y":"Contact customer service","SuW7xY8Z":"Logout","TvX8yZ9A":"Personal information","UwY9zA0B":"Shopping cart","VxZ0aB1C":"Checkout","WyA1bC2D":"Shipping fee","XzB2cD3E":"Order status","YaC3dE4F":"Payment successful","ZbD4eF5G":"Payment failed"}},"form":{"labelWidth":"6.2em","colon":false},"resetBtn":{"show":true,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"超前水平钻孔"}
      }
    }
  ]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| inventoryId | 1946056066395553800 | number | 是 | 清单id |
| inventoryName | 超前水平钻孔6 | string | 是 | 清单名称 |
| cateId | 1947230213406466000 | number | 是 | 所属分类 |
| beforeInventory | 0 | number | 是 | 前置 |
| remark | - | string | 是 | 备注 |
| status | 0 | string | 是 | 状态 0 草稿 1正常 2停用 |
| stepList | - | array | 是 | 所属工序 |
| stepList.stepId | 1945037202924871700 | number | 是 | 工序id |
| stepList.sort | 0 | number | 是 | 清单显示 |
| relationList | - | array | 是 | 权限配置 |
| relationList.assignType | 1 | string | 是 | 分配类型 1部门分配 2 岗位分配 |
| relationList.relationId | 1944667500893503500 | number | 是 | 关联id |
| groupList | - | array | 是 | 所属 |
| groupList.groupId | 1944968716408115201 | string | 是 | 组id |
| groupList.score | 5 | number | 是 | 分数 |
| groupList.formInfo | - | object | 是 | 表单相关 |
| groupList.formInfo.rulesJson | - | array | 是 | 表单的规则和字段的整体配置数据，通常包含多个字段的配置 |
| groupList.formInfo.rulesJson.type | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.field | Fmoamd723ppjaec | string | 是 | - |
| groupList.formInfo.rulesJson.title | 搭接进度 | string | 是 | - |
| groupList.formInfo.rulesJson.info | - | string | 是 | - |
| groupList.formInfo.rulesJson.$required | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_id | id_Fpylmd723ppjafc | string | 是 | - |
| groupList.formInfo.rulesJson.name | ref_Fohkmd723ppjagc | string | 是 | - |
| groupList.formInfo.rulesJson.display | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.hidden | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_drag_tag | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.value | 1 | number | 是 | - |
| groupList.formInfo.rulesJson.props | - | object | 是 | - |
| groupList.formInfo.rulesJson.props.activeValue | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.props.inactiveValue | false | boolean | 是 | - |
| groupList.formInfo.optionsJson | - | object | 是 | 表单的配置数据（例如：布局、尺寸、全局数据等） |
| groupList.formInfo.optionsJson.language | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.Az87OmQS | 商品名称 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.BAVvUidu | 商品价格 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.CkD1fG2H | 商品描述 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.DgH2iJ3K | 库存数量 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.EhI3jK4L | 发货方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.FiJ4kL5M | 配送时间 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.GjK5lM6N | 用户评价 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.HkL6mN7O | 添加到购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.IkM7nO8P | 立即购买 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.JlN8oP9Q | 优惠活动 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.KmO9pQ0R | 搜索商品 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.LnP0qR1S | 分类 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.MoQ1rS2T | 品牌 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.NpR2sT3U | 付款方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.OqS3tU4V | 订单确认 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.PrT4uV5W | 用户注册 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.QsU5vW6X | 用户登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.RtV6wX7Y | 联系客服 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.SuW7xY8Z | 退出登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.TvX8yZ9A | 个人信息 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.UwY9zA0B | 购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.VxZ0aB1C | 结算 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.WyA1bC2D | 运费 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.XzB2cD3E | 订单状态 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.YaC3dE4F | 支付成功 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.ZbD4eF5G | 支付失败 | string | 是 | - |
| groupList.formInfo.optionsJson.language.en | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.en.Az87OmQS | Goods name | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.BAVvUidu | Goods price | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.CkD1fG2H | Product description | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.DgH2iJ3K | Stock quantity | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.EhI3jK4L | Shipping method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.FiJ4kL5M | Delivery time | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.GjK5lM6N | User reviews | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.HkL6mN7O | Add to cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.IkM7nO8P | Buy now | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.JlN8oP9Q | Promotions | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.KmO9pQ0R | Search products | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.LnP0qR1S | Category | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.MoQ1rS2T | Brand | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.NpR2sT3U | Payment method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.OqS3tU4V | Order confirmation | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.PrT4uV5W | User registration | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.QsU5vW6X | User login | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.RtV6wX7Y | Contact customer service | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.SuW7xY8Z | Logout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.TvX8yZ9A | Personal information | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.UwY9zA0B | Shopping cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.VxZ0aB1C | Checkout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.WyA1bC2D | Shipping fee | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.XzB2cD3E | Order status | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.YaC3dE4F | Payment successful | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.ZbD4eF5G | Payment failed | string | 是 | - |
| groupList.formInfo.optionsJson.form | - | object | 是 | - |
| groupList.formInfo.optionsJson.form.labelWidth | 6.2em | string | 是 | - |
| groupList.formInfo.optionsJson.form.colon | false | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.innerText | 重置 | string | 是 | - |
| groupList.formInfo.optionsJson.submitBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.innerText | 提交 | string | 是 | - |
| groupList.formInfo.optionsJson.formName | 超前水平钻孔 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取清单详情

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:32:58

> 更新时间: 2025-07-25 11:32:58

```text
暂无描述
```

**接口状态**

> 需修改

**接口URL**

> /system/inventory/detail/1946056066395553793

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"inventoryId":"1946056066395553793","cateId":"1947230213406466049","cateName":"超前及地质不良作业线","inventoryName":"超前水平钻孔6","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":[{"inventoryId":"1946056066395553793","groupId":"1944968716408115201","groupName":"现场组","score":5,"formInfo":{"formId":"1947557864268750850","inventoryId":"1946056066395553793","groupId":"1944968716408115201","title":"超前水平钻孔","description":null,"rulesJson":[],"optionsJson":{}}}],"stepList":null,"relationList":null,"createTime":"2025-07-18 11:54:57"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.inventoryId | 1946056066395553793 | string | 清单id |
| data.cateId | 1947230213406466049 | string | 所属分类 |
| data.cateName | 超前及地质不良作业线 | string | 分类名称 |
| data.inventoryName | 超前水平钻孔6 | string | 清单名称 |
| data.beforeInventory | 0 | number | 前置 |
| data.beforeInventoryName | - | string | 前置清单名称 |
| data.remark | - | string | 备注 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.stepNames | 超前及地质不良作业线 | string | 所属 |
| data.deptNames | 技术部,工程管理 | string | 关联部门 |
| data.postNames | 技术员 | string | 关联岗位 |
| data.groupNames | 现场组 | string | 关联组 |
| data.groupList | - | array | 所属组 |
| data.groupList.inventoryId | 1946056066395553793 | string | 清单id |
| data.groupList.groupId | 1944968716408115201 | string | 组id |
| data.groupList.groupName | 现场组 | string | - |
| data.groupList.score | 5 | number | 分数 |
| data.groupList.formInfo | - | object | 表单相关 |
| data.groupList.formInfo.formId | 1947557864268750850 | string | 表单id |
| data.groupList.formInfo.inventoryId | 1946056066395553793 | string | 清单id |
| data.groupList.formInfo.groupId | 1944968716408115201 | string | 组id |
| data.groupList.formInfo.title | 超前水平钻孔 | string | 表单 |
| data.groupList.formInfo.description | - | null | 表单描述 |
| data.groupList.formInfo.rulesJson | - | array | 表单的规则和字段的整体配置数据，通常包含多个字段的配置 |
| data.groupList.formInfo.optionsJson | - | object | 表单的配置数据（例如：布局、尺寸、全局数据等） |
| data.stepList | - | null | 所属工序 |
| data.relationList | - | null | 权限配置 |
| data.createTime | 2025-07-18 11:54:57 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单列表(搜索列表)  

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:33:59

> 更新时间: 2025-07-25 11:33:59

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventory/listAll?inventoryName=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| inventoryName | - | string | 否 | 清单名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"inventoryId":"1947822446988038146","cateId":"1947819719310553089","cateName":null,"inventoryName":"超前水平钻孔","beforeInventory":0,"beforeInventoryName":null,"remark":"","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 08:53:55"},{"inventoryId":"1947856995080040449","cateId":"1947820129584787457","cateName":null,"inventoryName":"测试清单111","beforeInventory":"1947822446988038146","beforeInventoryName":null,"remark":"123","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 11:11:12"},{"inventoryId":"1947867289080655873","cateId":"1947820203786219522","cateName":null,"inventoryName":"书法大赛得分","beforeInventory":"1947856995080040449","beforeInventoryName":null,"remark":"123123","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 11:52:06"},{"inventoryId":"1947903030414372865","cateId":"1947820235595821058","cateName":null,"inventoryName":"111","beforeInventory":0,"beforeInventoryName":null,"remark":"","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 14:14:07"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.inventoryId | 1947822446988038146 | string | - |
| data.cateId | 1947819719310553089 | string | - |
| data.cateName | - | null | - |
| data.inventoryName | 超前水平钻孔 | string | 清单名称 |
| data.beforeInventory | 0 | number | - |
| data.beforeInventoryName | - | null | - |
| data.remark | - | string | - |
| data.status | 1 | string | - |
| data.stepNames | - | null | - |
| data.deptNames | - | null | - |
| data.postNames | - | null | - |
| data.groupNames | - | null | - |
| data.groupList | - | null | - |
| data.stepList | - | null | - |
| data.relationList | - | null | - |
| data.createTime | 2025-07-23 08:53:55 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:35:18

> 更新时间: 2025-07-25 11:35:18

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventory/1947224540304121858

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-28 14:34:31

> 更新时间: 2025-07-28 14:34:31

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp/pageListAll?pageNum=1&pageSize=100000&inventoryName=&beforeInventory=&status=&params[stepId]=&params[groupId]=&params[deptId]=&params[postId]=&cateId=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 100000 | string | 是 | 条数 |
| inventoryName | - | string | 否 | 清单名称 |
| beforeInventory | - | string | 否 | 前置清单 |
| status | - | string | 否 | 状态 0 草稿 1正常 2停用 |
| params[stepId] | - | string | 否 | 所属工序 |
| params[groupId] | - | string | 否 | 所属组 |
| params[deptId] | - | string | 否 | 所属部门 |
| params[postId] | - | string | 否 | 所属岗位 |
| cateId | - | string | 否 | 所属分类 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":8,"rows":[{"inventoryId":"1947554121854836738","cateId":0,"cateName":"","inventoryName":"超前水平钻孔999","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-22 15:07:41"},{"inventoryId":"1947457252814315521","cateId":"1947230213406466049","cateName":"超前及地质不良作业线","inventoryName":"超前水平钻孔99","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-22 08:42:46"},{"inventoryId":"1946056066395553793","cateId":0,"cateName":"","inventoryName":"超前水平钻孔6","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:54:57"},{"inventoryId":"1946055074157441025","cateId":0,"cateName":"","inventoryName":"超前水平钻孔4","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:51:00"},{"inventoryId":"1946050391804334082","cateId":0,"cateName":"","inventoryName":"超前水平钻孔3","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:32:24"},{"inventoryId":"1945775059629109250","cateId":0,"cateName":"","inventoryName":"超前水平钻孔2","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 17:18:20"},{"inventoryId":"1945770211009490946","cateId":0,"cateName":"","inventoryName":"超前水平钻孔1","beforeInventory":"1945759589827887105","beforeInventoryName":"超前水平钻孔","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 16:59:04"},{"inventoryId":"1945759589827887105","cateId":0,"cateName":"","inventoryName":"超前水平钻孔","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 16:16:51"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 8 | number | - |
| rows | - | array | - |
| rows.inventoryId | 1947554121854836738 | string | 清单id |
| rows.cateId | 0 | number | 所属分类 |
| rows.cateName | - | string | 所属分类名称 |
| rows.inventoryName | 超前水平钻孔999 | string | 清单名称 |
| rows.beforeInventory | 0 | number | 前置清单 |
| rows.beforeInventoryName | - | string | 前置清单名称 |
| rows.remark | - | string | 备注 |
| rows.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| rows.stepNames | 超前及地质不良作业线 | string | 所属工序 |
| rows.deptNames | 技术部,工程管理 | string | 所属部门 |
| rows.postNames | 技术员 | string | 所属 |
| rows.groupNames | 现场组 | string | 所属 |
| rows.groupList | - | null | - |
| rows.stepList | - | null | - |
| rows.relationList | - | null | - |
| rows.createTime | 2025-07-22 15:07:41 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 同步清单模版

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-28 14:52:39

> 更新时间: 2025-07-29 18:02:46

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/inventory/sync

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
[1949668679096549377,1949672346071678977,1949672995438018562]
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

### 系统管理

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 10:26:03

> 更新时间: 2025-07-23 10:26:03

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 后台上传

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-01 15:32:54

> 更新时间: 2025-08-05 14:43:16

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /resource/oss/upload

**请求方式**

> POST

**Content-Type**

> form-data

**请求Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| file | /Users/<USER>/Downloads/WechatIMG53902.jpg | file | 是 | file |
| serviceType | 2 | string | 是 | 服务类型 1业务 2图片；3视频 4 音频 5 文档 6 zip |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "url": "http://************:9000/file-xdc/image/2025/08/01/6219e811d5c7499c8144e0c734eece6c.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250801T073154Z&X-Amz-SignedHeaders=host&X-Amz-Credential=xdc%2F20250801%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Expires=3600&X-Amz-Signature=79c32703f144f67fcbaf943676ef844f3d628e9e8aa343be44cf1a6a465cfeb8",
        "fileName": "WechatIMG53902.jpg",
        "ossId": "1951184097120468993"
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.url | http://************:9000/file-xdc/image/2025/08/01/6219e811d5c7499c8144e0c734eece6c.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250801T073154Z&X-Amz-SignedHeaders=host&X-Amz-Credential=xdc%2F20250801%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Expires=3600&X-Amz-Signature=79c32703f144f67fcbaf943676ef844f3d628e9e8aa343be44cf1a6a465cfeb8 | string | 访问 |
| data.fileName | WechatIMG53902.jpg | string | 文件 |
| data.ossId | 1951184097120468993 | string | 附件 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 流程

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 14:56:58

> 更新时间: 2025-07-28 14:56:58

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 查询流程管理-流程列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:14:26

> 更新时间: 2025-07-28 15:14:26

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/list

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 查询流程管理-流程列表(选项)

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:15:32

> 更新时间: 2025-07-28 15:15:32

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/select

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 查询流程管理-条件列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:16:05

> 更新时间: 2025-08-01 10:04:34

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/flowConditions/list/adjust

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取流程管理-流程详细信息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:18:52

> 更新时间: 2025-07-28 15:18:52

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/{flowId}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| flowId | - | string | 是 | - |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取FlowCode

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:19:22

> 更新时间: 2025-07-28 15:19:42

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/getFlowCode

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取流程节点字段

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:20:40

> 更新时间: 2025-07-28 15:20:40

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/getFlowNodeField/{flowCode}

**请求方式**

> GET

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| flowCode | - | string | 是 | - |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 新增流程管理-流程

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:21:13

> 更新时间: 2025-07-28 15:21:13

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/

**请求方式**

> POST

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改流程管理-流程

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:21:35

> 更新时间: 2025-07-28 15:21:35

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/

**请求方式**

> PUT

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改流程管理-流程状态

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:22:07

> 更新时间: 2025-07-28 15:22:07

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/changeStatus

**请求方式**

> PUT

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 流程设计

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:22:31

> 更新时间: 2025-07-30 14:09:47

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/designFlow

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "flowId":"",
    "flowName":"",
    "nodeConfig":{
        "nodeId":"",
        "flowId":"",
        "parentId":"",
        "nodeName":"",
        "type":"",
        "priorityLevel":"",
        "settype":"",
        "examineMode":"",
        "noHanderAction":"",
        "noHanderActionUserId":"",
        "nodeUserList":[],
        "conditionList":[],
        "childNode":{},
        "conditionNodes":"",
        "fields":[]
    },
    "isPublish":""
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除流程管理-流程

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 15:22:59

> 更新时间: 2025-07-28 15:22:59

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/flow/{flowIds}

**请求方式**

> DELETE

**Content-Type**

> none

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| flowIds | - | string | 是 | - |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 未命名接口

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-04 11:16:02

> 更新时间: 2025-08-04 11:16:02

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /resource/oss/list

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 任务

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 11:14:37

> 更新时间: 2025-08-05 11:14:37

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 任务列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 11:21:46

> 更新时间: 2025-08-05 15:20:34

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/appraiseTask/list?pageNum=1&pageSize=15&taskType=1&taskName=&examineStatus=0

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | - |
| pageSize | 15 | string | 是 | - |
| taskType | 1 | string | 是 | 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| taskName | - | string | 是 | - |
| examineStatus | 0 | string | 是 | 0全部任务 1待审批 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":2,"rows":[{"taskId":"1952570606541148161","taskName":"员工ysf入职审批单","taskStatus":"1","nodeId":"1952549805876068359","tableId":null,"currentStep":"审核人","taskType":"1","relationId":"1952570605547098113","createTime":"2025-08-05 11:21:24","createBy":"1952570605547098113","createByName":"ysf","isApprove":"0","remake":null},{"taskId":"1952559738931683329","taskName":"员工入职审批审批单","taskStatus":"2","nodeId":"1952549805876068359","tableId":null,"currentStep":"审核人","taskType":"1","relationId":"1952559738017325058","createTime":"2025-08-05 10:39:11","createBy":"1952559738017325058","createByName":"whb","isApprove":"0","remake":null}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.taskId | 1952570606541148161 | string | taskId |
| rows.taskName | 员工ysf入职审批单 | string | 任务名称 |
| rows.taskStatus | 1 | string | 任务状态 1正在运行；2结束；3异常 |
| rows.nodeId | 1952549805876068359 | string | - |
| rows.tableId | - | null | - |
| rows.currentStep | 审核人 | string | 当前 |
| rows.taskType | 1 | string | 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| rows.relationId | 1952570605547098113 | string | - |
| rows.createTime | 2025-08-05 11:21:24 | string | 创建时间 |
| rows.createBy | 1952570605547098113 | string | - |
| rows.createByName | ysf | string | 创建 |
| rows.isApprove | 0 | string | - |
| rows.remake | - | null | - |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 任务详情

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 11:23:14

> 更新时间: 2025-08-05 15:20:31

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/appraiseTask/getInfo?taskId=1952559738931683329

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| taskId | 1952559738931683329 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"taskId":"1952559738931683329","name":"员工入职审批审批单","taskStatus":"2","currentStep":"审核人","createTime":"2025-08-05 10:39:11","nodeInfoList":[{"nodeName":"审核人","type":"1","status":"1","userInfoList":[{"evaluatorName":"沈勇","appraiseStatus":"1","remark":null,"updateTime":"2025-08-05 11:10:01"}]},{"nodeName":"抄送人","type":"2","status":"1","userInfoList":[{"evaluatorName":"测试1","appraiseStatus":"0","remark":null,"updateTime":"2025-08-05 10:39:11"}]}],"taskType":"1","isApprove":"0","formValue":[{"fieldName":"pointIds","fieldLabel":"工点组","fieldValue":["1946046887033368578"],"fieldPerm":"1","valueLables":[{"value":"1946046887033368578","label":"二号工点"},{"value":"1945383046872064002","label":"一号工点"}]},{"fieldName":"label","fieldLabel":"员工标签","fieldValue":"0","fieldPerm":"1","valueLables":[{"value":"0","label":"派遣人员"},{"value":"1","label":"外协人员"}]},{"fieldName":"employeeType","fieldLabel":"员工类型","fieldValue":"1","fieldPerm":"1","valueLables":[{"value":"1","label":"职工"},{"value":"2","label":"劳务"},{"value":"3","label":"外聘"},{"value":"4","label":"实习生"}]},{"fieldName":"groupId","fieldLabel":"员工组","fieldValue":"1944968716408115201","fieldPerm":"1","valueLables":[{"value":"1944968716408115201","label":"测试组2"},{"value":"1951219463240069122","label":"现场组"},{"value":"1951219463240069123","label":"内业组"},{"value":"1946051911243345921","label":"测试组1"}]},{"fieldName":"aGroupId","fieldLabel":"考勤组","fieldValue":null,"fieldPerm":"1","valueLables":[{"value":"1949629302463074306","label":"测试"},{"value":"1949632906158776322","label":"固定班"},{"value":"1949648523775549441","label":"测试1"}]},{"fieldName":"deptPosts","fieldLabel":"部门岗位组","fieldValue":[{"deptId":"1951220450126213121","postId":"1947822895516418049","dataScope":"1"}],"fieldPerm":"1","valueLables":null}],"remake":null}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.taskId | 1952559738931683329 | string | 任务 |
| data.name | 员工入职审批审批单 | string | 任务名称 |
| data.taskStatus | 2 | string | 任务状态 1正在运行；2结束；3异常 |
| data.currentStep | 审核人 | string | 当前节点 |
| data.createTime | 2025-08-05 10:39:11 | string | 创建 |
| data.nodeInfoList | - | array | - |
| data.nodeInfoList.nodeName | 审核人 | string | 节点名称 |
| data.nodeInfoList.type | 1 | string | 点类型 （0 发起人 1审批 2抄送 3条件 4路由 ） |
| data.nodeInfoList.status | 1 | string | 0 待审批 1已审批 |
| data.nodeInfoList.userInfoList | - | array | - |
| data.nodeInfoList.userInfoList.evaluatorName | 沈勇 | string | 审批用户 |
| data.nodeInfoList.userInfoList.appraiseStatus | 1 | string | 审批状态0待审批1已通过2已驳回3已跳过4已转办 |
| data.nodeInfoList.userInfoList.remark | - | null | - |
| data.nodeInfoList.userInfoList.updateTime | 2025-08-05 11:10:01 | string | - |
| data.taskType | 1 | string | 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| data.isApprove | 0 | string | - |
| data.formValue | - | array | - |
| data.formValue.fieldName | pointIds | string | - |
| data.formValue.fieldLabel | 工点组 | string | 字段 |
| data.formValue.fieldValue | - | array | 字段 |
| data.formValue.fieldPerm | 1 | string | - |
| data.formValue.fieldValue.deptId | 1951220450126213121 | string | - |
| data.formValue.fieldValue.postId | 1947822895516418049 | string | - |
| data.formValue.fieldValue.dataScope | 1 | string | - |
| data.remake | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

## app端

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-11 09:27:56

> 更新时间: 2025-07-11 11:24:02

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | 428a8310cd442757ae699df5d894f051 | string | 是 | 客户端ID |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | 428a8310cd442757ae699df5d894f051 | string | 是 | 客户端ID |

**Query**

### 登录

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-14 15:17:50

> 更新时间: 2025-08-08 18:04:27

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/frontAuth/login

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"clientId": "428a8310cd442757ae699df5d894f051",
	"grantType": "password",
	"phonenumber": "18636984056",
	"password": "xdc@xdc1"
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 退出登录

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-30 08:44:58

> 更新时间: 2025-07-30 10:23:24

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/frontAuth/logout

**请求方式**

> POST

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 注册

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 17:45:37

> 更新时间: 2025-08-08 16:19:47

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/frontAuth/register

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"clientId": "428a8310cd442757ae699df5d894f051",
	"grantType": "password",
	"phonenumber": "13432564387",
	"password": "123456",
	"nickName":"华盛顿",
	"birthday":"1980-06-05",
	"sex":"0",
	"label":"0",
	"tenantId":"475158",       //所属项目部
	"groupId":1944968716408115201,  //组id
	"pointIds":["1946046887033368578"], //工点id
	"employeeType":"1",
	"aGroupId":1949629302463074306, //考勤组id
	"workNum":"1234",//工号
	"workYear":"123",//工作年限
	"duties":1,
	"deptPosts":[{    //部门岗位组
		"deptId":1951220450126213121,
		"postId":1947823414850945026,
		"dataScope":"3"
	},{
		"deptId": 1951220490001461250,
		"postId": 1947823414850945026,
		"dataScope": "1"
	}, {
		"deptId": 1951220490001461250,
		"postId": 1947822895516418049,
		"dataScope": "1"
	}]
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 获取用户基础信息

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-25 10:40:09

> 更新时间: 2025-08-01 18:06:46

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/getInfo

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "permissions": [
            {
                "name": "员工调整",
                "roleKey": "system:frontUserChange"
            },
            {
                "name": "日报评价",
                "roleKey": "system:frontUserDay"
            },
            {
                "name": "周考评价",
                "roleKey": "system:frontUserWeek"
            },
            {
                "name": "员工打卡",
                "roleKey": "system:frontUserCheckIn"
            },
            {
                "name": "任务下发",
                "roleKey": "system:frontUserTask"
            }
        ],
        "user": {
            "userId": "1948558429087907842",
            "tenantId": "475158",
            "tenantName": "第一项目部",
            "nickName": "13213",
            "phonenumber": "18636984075",
            "sex": "0",
            "empStatus": "0",
            "deptPostStr": "测试1-研发主管"
        }
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.permissions | - | array | 用户菜单权限 |
| data.permissions.name | 员工调整 | string | 权限名 |
| data.permissions.roleKey | system:frontUserChange | string | 权限标识 |
| data.user | - | object | 用户基础信息 |
| data.user.userId | 1948558429087907842 | string | 员工ID |
| data.user.tenantId | 475158 | string | 员工项目部 |
| data.user.tenantName | 第一项目部 | string | 所属项目部 |
| data.user.nickName | 13213 | string | 员工姓名 |
| data.user.phonenumber | 18636984075 | string | 手机号 |
| data.user.sex | 0 | string | 用户性别（0男 1女 |
| data.user.empStatus | 0 | string | 员工状态（0在职 1离职 2待入职3调岗中4已归档） |
| data.user.deptPostStr | 测试1-研发主管 | string | 员工部门岗位信息 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 获取员工所属项目部

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-28 17:33:52

> 更新时间: 2025-07-28 17:34:39

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/multiTenant

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "tenantId": "475158",
            "projectName": "第一项目部"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.tenantId | 475158 | string | 项目部编号 |
| data.projectName | 第一项目部 | string | 项目部名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 查询施工工点信息列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 17:50:34

> 更新时间: 2025-08-05 14:17:03

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/selectWp

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "tenantId": "475158",
            "projectName": "第一项目部"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.tenantId | 475158 | string | 项目部编号 |
| data.projectName | 第一项目部 | string | 项目部名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 部门列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 17:51:30

> 更新时间: 2025-08-05 14:50:26

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/deptTree

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "tenantId": "475158",
            "projectName": "第一项目部"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.tenantId | 475158 | string | 项目部编号 |
| data.projectName | 第一项目部 | string | 项目部名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 组列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 17:52:25

> 更新时间: 2025-07-28 17:52:25

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/selectGroup

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "tenantId": "475158",
            "projectName": "第一项目部"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.tenantId | 475158 | string | 项目部编号 |
| data.projectName | 第一项目部 | string | 项目部名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 岗位列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 17:53:36

> 更新时间: 2025-07-28 17:53:36

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/selectPost

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "tenantId": "475158",
            "projectName": "第一项目部"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.tenantId | 475158 | string | 项目部编号 |
| data.projectName | 第一项目部 | string | 项目部名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 考勤组基础信息列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-28 17:55:37

> 更新时间: 2025-08-05 14:17:06

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/selectAg

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "tenantId": "475158",
            "projectName": "第一项目部"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.tenantId | 475158 | string | 项目部编号 |
| data.projectName | 第一项目部 | string | 项目部名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 切换项目部

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-30 09:04:12

> 更新时间: 2025-07-30 09:14:02

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/frontAuth/changeTenant

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"tenantId": "475158"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| tenantId | 475158 | string | 是 | 项目部编号 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "scope": null,
        "openid": null,
        "isMulti": null,
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6IjE5NDU2NTYyNTE5MDE4OTA1NjEiLCJyblN0ciI6InFvSXdiRFB2RktiSGVMc2VjUzRBTEdGZFM1dXNKTUYxIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiNDc1MTU4IiwidXNlcklkIjoxOTQ1NjU2MjUxOTAxODkwNTYxLCJ1c2VyTmFtZSI6IjE4NjM2OTg0MDU2In0.FjSAzrBV0wE4jEC0XhVXZGUPjopFi2eQfMHVn4YYC-g",
        "refresh_token": null,
        "expire_in": 604206,
        "refresh_expire_in": null,
        "client_id": "428a8310cd442757ae699df5d894f051"
    }
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 所属项目部列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-01 11:46:36

> 更新时间: 2025-08-01 11:46:46

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/selectXmb

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "tenantId": "475158",
            "projectName": "第一项目部"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.tenantId | 475158 | string | 项目部编号 |
| data.projectName | 第一项目部 | string | 项目部名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 个人中心

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:06:12

> 更新时间: 2025-07-31 09:30:48

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 证书列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:07:12

> 更新时间: 2025-07-30 16:13:56

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/getCertList

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 新增证书

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:18:54

> 更新时间: 2025-07-30 16:18:54

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/addCert

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "no":"123",
    "name":"123",
    "type":0,
    "time":"2025-07-29",
    "level":"1级",
    "ossId":123,
    "remark":"124"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取证书详情

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:21:49

> 更新时间: 2025-07-30 16:21:49

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/getCertList/1950471142783340546

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改证书

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:27:05

> 更新时间: 2025-07-30 16:27:05

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/editCert

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "id":1950471142783340546,
    "no":"123",
    "name":"123sdf",
    "type":0,
    "time":"2025-07-29",
    "level":"1级",
    "ossId":123,
    "remark":"124"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除证书

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:27:54

> 更新时间: 2025-07-30 16:27:54

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/deleteCert/1950471142783340546

**请求方式**

> DELETE

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "id":1950471142783340546,
    "no":"123",
    "name":"123sdf",
    "type":0,
    "time":"2025-07-29",
    "level":"1级",
    "ossId":123,
    "remark":"124"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改密码

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:37:00

> 更新时间: 2025-07-30 16:37:00

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/rePassword

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "phonenumber":13412341234,
    "oldPassword":"123456",
    "newPassword1":"qwe123",
    "newPassword2":"qwe123"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取个人信息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 16:38:18

> 更新时间: 2025-07-30 17:23:15

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/getPersonInfo

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 修改个人信息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-30 17:41:23

> 更新时间: 2025-08-04 11:52:17

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/editPersonInfo

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "userId": "1951209591937171458",
  "userName": "13412341284",
  "nickName": "whb1",
  "phonenumber": "13412341284",
  "sex": "1",
  "label": "0",
  "groupId": "1947826548543401988",
  "entryTime": null,
  "workNum": "234",
  "remark": null,
  "birthday": null,
  "duties": null,
  "graduationSchool": null,
  "workYear": null,
  "education": null,
  "employeeType": "1",
  "pointIds": [
    "1950030001818673154"
  ]
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取入职审批进度

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-31 09:07:06

> 更新时间: 2025-07-31 09:07:06

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/getRegisterPass

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取入职审批进度详情

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-31 09:30:30

> 更新时间: 2025-07-31 09:30:30

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/personnelCenter/getRegisterPassInfo/123

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 审批任务

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-31 09:46:20

> 更新时间: 2025-08-07 10:30:55

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 获取审批任务列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-31 09:48:33

> 更新时间: 2025-08-06 15:49:57

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/task/list?pageNum=1&pageSize=10&examineStatus=0&taskName=&taskType=

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | - |
| pageSize | 10 | string | 是 | - |
| examineStatus | 0 | string | 是 | 0全部任务 1待审批 |
| taskName | - | string | 是 | 搜索任务名称 |
| taskType | - | string | 是 | 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":2,"rows":[{"taskId":"1951191815533133825","taskName":"员工入职审批审批单","taskStatus":"1","nodeId":"1951100640440680451","tableId":null,"currentStep":"审核人","taskType":"1","relationId":"1951191815050788866","createTime":"2025-08-01 16:02:56","createBy":"1951191815050788866","createByName":"whb","isApprove":"1","remake":null},{"taskId":"1951190887816077314","taskName":"员工入职审批审批单","taskStatus":"1","nodeId":"1951100640440680451","tableId":null,"currentStep":"审核人","taskType":"1","relationId":"1951190887321149442","createTime":"2025-08-01 15:58:54","createBy":"1951190887321149442","createByName":null,"isApprove":"1","remake":null}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.taskId | 1951191815533133825 | string | - |
| rows.taskName | 员工入职审批审批单 | string | 搜索任务名称 |
| rows.taskStatus | 1 | string | 1正在运行；2结束；3异常 |
| rows.nodeId | 1951100640440680451 | string | - |
| rows.tableId | - | null | - |
| rows.currentStep | 审核人 | string | - |
| rows.taskType | 1 | string | 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| rows.relationId | 1951191815050788866 | string | - |
| rows.createTime | 2025-08-01 16:02:56 | string | - |
| rows.createBy | 1951191815050788866 | string | - |
| rows.createByName | whb | string | - |
| rows.isApprove | 1 | string | 0无审批权限 1有审批权限 |
| rows.remake | - | null | - |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取审批任务详情

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-07-31 09:47:40

> 更新时间: 2025-08-07 17:43:09

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/task/getTaskInfo?taskId=1953385316517715970

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| taskId | 1953385316517715970 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"taskId":"1953385316517715970","name":"cby调岗审批单","taskStatus":"1","currentStep":"审核人","createTime":"2025-08-07 17:18:46","nodeInfoList":[{"nodeName":"发起人","type":"0","status":"1","userInfoList":[{"evaluatorName":"hsy","appraiseStatus":"1","remark":null,"updateTime":"2025-08-07 17:18:46"}]},{"nodeName":"审核人","type":"1","status":"0","userInfoList":[{"evaluatorName":"hsy","appraiseStatus":"0","remark":null,"updateTime":"2025-08-07 17:18:46"}]}],"taskType":"3","isApprove":"0","formValue":[{"fieldName":"switchUser","fieldLabel":"选择员工","fieldValue":"1952551639793594370","fieldPerm":"1","valueLables":[{"value":"1952551639793594370","label":"cby"}]},{"fieldName":"currentPostStatus","fieldLabel":"员工当前岗位","fieldValue":"1944678525604741122","fieldPerm":"1","valueLables":[{"value":"1944678525604741122","label":"工程部长"},{"value":"1947193690327937026","label":"主管"},{"value":"1947822895516418049","label":"技术主管"},{"value":"1947823414850945026","label":"研发主管"},{"value":"1951220760131416065","label":"总工"},{"value":"1951220929258336257","label":"技术员"}]},{"fieldName":"currentDeptState","fieldLabel":"员工当前部门","fieldValue":"1945307819278397441","fieldPerm":"1","valueLables":[{"value":"1945307819177734145","label":"第一项目部"},{"value":"1945307819278397441","label":"测试"},{"value":"1945307819278397442","label":"测试1"},{"value":"1951220450126213121","label":"技术部"},{"value":"1951220490001461250","label":"工程部"},{"value":"1951220531285995521","label":"商务部"},{"value":"1951220594708066305","label":"办公室"},{"value":"1951220629956997122","label":"人事部"}]},{"fieldName":"adjustPostStatus","fieldLabel":"员工调整岗位","fieldValue":"1944678525604741122","fieldPerm":"1","valueLables":[{"value":"1944678525604741122","label":"工程部长"},{"value":"1947193690327937026","label":"主管"},{"value":"1947822895516418049","label":"技术主管"},{"value":"1947823414850945026","label":"研发主管"},{"value":"1951220760131416065","label":"总工"},{"value":"1951220929258336257","label":"技术员"}]},{"fieldName":"adjustDeptState","fieldLabel":"员工调整部门","fieldValue":"1951220490001461250","fieldPerm":"1","valueLables":[{"value":"1945307819177734145","label":"第一项目部"},{"value":"1945307819278397441","label":"测试"},{"value":"1945307819278397442","label":"测试1"},{"value":"1951220450126213121","label":"技术部"},{"value":"1951220490001461250","label":"工程部"},{"value":"1951220531285995521","label":"商务部"},{"value":"1951220594708066305","label":"办公室"},{"value":"1951220629956997122","label":"人事部"}]},{"fieldName":"remake","fieldLabel":"调整原因","fieldValue":"123","fieldPerm":"1","valueLables":[]}],"remake":"123"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.taskId | 1953385316517715970 | string | 任务 |
| data.name | cby调岗审批单 | string | 任务名称 |
| data.taskStatus | 1 | string | 任务状态 1正在运行；2结束；3异常 |
| data.currentStep | 审核人 | string | 当前节点 |
| data.createTime | 2025-08-07 17:18:46 | string | 创建 |
| data.nodeInfoList | - | array | - |
| data.nodeInfoList.nodeName | 发起人 | string | 节点名称 |
| data.nodeInfoList.type | 0 | string | 点类型 （0 发起人 1审批 2抄送 3条件 4路由 ） |
| data.nodeInfoList.status | 1 | string | 0 待审批 1已审批 |
| data.nodeInfoList.userInfoList | - | array | - |
| data.nodeInfoList.userInfoList.evaluatorName | hsy | string | 审批用户 |
| data.nodeInfoList.userInfoList.appraiseStatus | 1 | string | 审批状态0待审批1已通过2已驳回3已跳过4已转办 |
| data.nodeInfoList.userInfoList.remark | - | null | - |
| data.nodeInfoList.userInfoList.updateTime | 2025-08-07 17:18:46 | string | - |
| data.taskType | 3 | string | 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| data.isApprove | 0 | string | - |
| data.formValue | - | array | - |
| data.formValue.fieldName | switchUser | string | - |
| data.formValue.fieldLabel | 选择员工 | string | 字段 |
| data.formValue.fieldValue | 1952551639793594370 | string | 字段 |
| data.formValue.fieldPerm | 1 | string | - |
| data.formValue.valueLables | - | array | - |
| data.formValue.valueLables.value | 1952551639793594370 | string | - |
| data.formValue.valueLables.label | cby | string | - |
| data.remake | 123 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 审批入职任务

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-01 17:20:01

> 更新时间: 2025-08-08 16:27:28

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/registerTask/approveRegister

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "taskId":1952983585371734018,
    "taskStatus":1,  //1通过2驳回
    "formValue":[
        {
            "fieldName":"deptPosts",  //字段名字
            "fieldValue":[{  //字段值
                "deptId":1951220450126213121,
                "postId":1947823414850945026,
                "dataScope":"3"
            },{
                "deptId": 1951220490001461250,
                "postId": 1947823414850945026,
                "dataScope": "1"
            }, {
                "deptId": 1951220490001461250,
                "postId": 1947822895516418049,
                "dataScope": "1"
            }]
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| taskId | 1952983585371734000 | number | 是 | 任务 |
| taskStatus | 1 | number | 是 | 1通过2驳回 |
| formValue | - | array | 是 | - |
| formValue.fieldName | deptPosts | string | 是 | 字段名字 |
| formValue.fieldValue | - | array | 是 | 字段 |
| formValue.fieldValue.deptId | 1951220450126213000 | number | 是 | 部门id |
| formValue.fieldValue.postId | 1947823414850945000 | number | 是 | 岗位id |
| formValue.fieldValue.dataScope | 3 | string | 是 | 数据权限 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取离职审批字段

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 16:18:05

> 更新时间: 2025-08-08 08:47:42

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/adjustTask/getAdjustInfo

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"fieldName":"adjustStatus","fieldLabel":"员工调整状态","fieldValue":null,"fieldPerm":"1","valueLables":[{"value":"0","label":"在职"},{"value":"1","label":"离职"}]},{"fieldName":"switchUser","fieldLabel":"选择员工","fieldValue":null,"fieldPerm":"1","valueLables":[{"value":"1952551639793594370","label":"cby"},{"value":"1952559738017325058","label":"whb"}]},{"fieldName":"remake","fieldLabel":"调整原因","fieldValue":null,"fieldPerm":"1","valueLables":null},{"fieldName":"currentState","fieldLabel":"员工当前状态","fieldValue":null,"fieldPerm":"0","valueLables":[{"value":"0","label":"在职"},{"value":"1","label":"离职"}]}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.fieldName | adjustStatus | string | 字段 |
| data.fieldLabel | 员工调整状态 | string | 字段中文 |
| data.fieldValue | - | null | 字段 |
| data.fieldPerm | 1 | string | 是否可编辑 |
| data.valueLables | - | null | 字段选择框的 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取员工当前状态

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-07 09:55:54

> 更新时间: 2025-08-08 09:04:35

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/adjustTask/getCurrentState?userId=1952911936920252418

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| userId | 1952911936920252418 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"fieldName":"currentState","fieldLabel":"员工状态","fieldValue":"3","fieldPerm":"0","valueLables":[{"value":"0","label":"在职"},{"value":"1","label":"离职"}]}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.fieldName | currentState | string | 字段 |
| data.fieldLabel | 员工状态 | string | 字段中文 |
| data.fieldValue | 3 | string | 字段 |
| data.fieldPerm | 0 | string | 是否可编辑 |
| data.valueLables | - | array | 字段选择框的 |
| data.valueLables.value | 0 | string | - |
| data.valueLables.label | 在职 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 提交离职信息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 17:06:09

> 更新时间: 2025-08-07 17:04:35

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/adjustTask/insertByAdjust

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "formValue":[
        {
			"fieldName": "switchUser",
			"fieldLabel": "选择员工",
			"fieldValue": 1952911936920252418,
			"fieldPerm": "1",
			"valueLables": null
		},
        {
			"fieldName": "currentState",
			"fieldLabel": "员工当前状态",
			"fieldValue": 1,
			"fieldPerm": "1",
			"valueLables": null
		},
        {
			"fieldName": "adjustStatus",
			"fieldLabel": "员工调整状态",
			"fieldValue": 0,
			"fieldPerm": "1",
			"valueLables": null
		},
		{
			"fieldName": "remake",
			"fieldLabel": "调整原因",
			"fieldValue": "没理由",
			"fieldPerm": "1",
			"valueLables": null
		}
    ]
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 审批离职任务

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-06 11:24:08

> 更新时间: 2025-08-06 14:42:14

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/adjustTask/taskApproveAdjust

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "taskId":1952938092293799937,
    "taskStatus":1,  //1通过2驳回
    "formValue":[
        {
            "fieldName":"adjustStatus",
            "fieldValue": 0
        }
    ]
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取选择人员

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-06 15:27:53

> 更新时间: 2025-08-08 16:21:13

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/task/getSwitchUserList

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"value":"1952551639793594370","label":"cby"},{"value":"1952559738017325058","label":"whb"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.value | 1952551639793594370 | string | value |
| data.label | cby | string | label |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取调岗审批字段

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-06 15:49:44

> 更新时间: 2025-08-08 16:22:04

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> app/front/deptPostTask/getDeptPostInfo

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"fieldName":"switchUser","fieldLabel":"选择员工","fieldValue":null,"fieldPerm":"1","valueLables":[{"value":"1952551639793594370","label":"cby"},{"value":"1952559738017325058","label":"whb"}]},{"fieldName":"currentPostStatus","fieldLabel":"员工当前岗位","fieldValue":null,"fieldPerm":"0","valueLables":[{"value":"1944678525604741122","label":"工程部长"},{"value":"1947193690327937026","label":"主管"},{"value":"1947822895516418049","label":"技术主管"},{"value":"1947823414850945026","label":"研发主管"},{"value":"1951220760131416065","label":"总工"},{"value":"1951220929258336257","label":"技术员"}]},{"fieldName":"currentDeptState","fieldLabel":"员工当前部门","fieldValue":null,"fieldPerm":"0","valueLables":[{"value":"1945307819177734145","label":"第一项目部"},{"value":"1945307819278397441","label":"测试"},{"value":"1945307819278397442","label":"测试1"},{"value":"1951220450126213121","label":"技术部"},{"value":"1951220490001461250","label":"工程部"},{"value":"1951220531285995521","label":"商务部"},{"value":"1951220594708066305","label":"办公室"},{"value":"1951220629956997122","label":"人事部"}]},{"fieldName":"remake","fieldLabel":"调整原因","fieldValue":null,"fieldPerm":"1","valueLables":null},{"fieldName":"adjustPostStatus","fieldLabel":"员工调整岗位","fieldValue":null,"fieldPerm":"1","valueLables":[{"value":"1944678525604741122","label":"工程部长"},{"value":"1947193690327937026","label":"主管"},{"value":"1947822895516418049","label":"技术主管"},{"value":"1947823414850945026","label":"研发主管"},{"value":"1951220760131416065","label":"总工"},{"value":"1951220929258336257","label":"技术员"}]},{"fieldName":"adjustDeptState","fieldLabel":"员工调整部门","fieldValue":null,"fieldPerm":"1","valueLables":[{"value":"1945307819177734145","label":"第一项目部"},{"value":"1945307819278397441","label":"测试"},{"value":"1945307819278397442","label":"测试1"},{"value":"1951220450126213121","label":"技术部"},{"value":"1951220490001461250","label":"工程部"},{"value":"1951220531285995521","label":"商务部"},{"value":"1951220594708066305","label":"办公室"},{"value":"1951220629956997122","label":"人事部"}]}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.fieldName | switchUser | string | 字段 |
| data.fieldLabel | 选择员工 | string | 字段中文 |
| data.fieldValue | - | null | 字段 |
| data.fieldPerm | 1 | string | 是否可编辑 |
| data.valueLables | - | array | 字段选择框的 |
| data.valueLables.value | 1952551639793594370 | string | - |
| data.valueLables.label | cby | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取员工当前部门岗位

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-07 10:19:51

> 更新时间: 2025-08-08 16:23:13

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/deptPostTask/getPersonDeptPost?userId=1952983584180551681

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| userId | 1952983584180551681 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"frontUserId":"1952983584180551681","deptId":"1951220450126213121","postId":"1947823414850945026","dataScope":"3"},{"frontUserId":"1952983584180551681","deptId":"1951220490001461250","postId":"1947822895516418049","dataScope":"3"},{"frontUserId":"1952983584180551681","deptId":"1951220490001461250","postId":"1947823414850945026","dataScope":"3"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.frontUserId | 1952983584180551681 | string | - |
| data.deptId | 1951220450126213121 | string | 部门id |
| data.postId | 1947823414850945026 | string | 岗位id |
| data.dataScope | 3 | string | 数据权限 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 提交调岗信息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-06 15:56:24

> 更新时间: 2025-08-11 10:20:39

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/deptPostTask/insertByDeptPost

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "formValue":[
        {
			"fieldName": "switchUser",
			"fieldLabel": "选择员工",
			"fieldValue": 1952551639793594370,
			"fieldPerm": "1",
			"valueLables": null
		},
        {
			"fieldName": "currentPostStatus",
			"fieldLabel": "员工当前岗位",
			"fieldValue": 1944678525604741122,
			"fieldPerm": "1",
			"valueLables": null
		},
		{
			"fieldName": "currentDeptState",
			"fieldLabel": "员工当前部门",
			"fieldValue": 1945307819278397441,
			"fieldPerm": "1",
			"valueLables": null
		},
        {
			"fieldName": "adjustPostStatus",
			"fieldLabel": "员工调整岗位",
			"fieldValue": 1944678525604741122,
			"fieldPerm": "1",
			"valueLables": null
		},
		{
			"fieldName": "adjustDeptState",
			"fieldLabel": "员工调整部门",
			"fieldValue": 1951220490001461250,
			"fieldPerm": "1",
			"valueLables": null
		},
		{
			"fieldName": "remake",
			"fieldLabel": "调整原因",
			"fieldValue": "123",
			"fieldPerm": "1",
			"valueLables": null
		}
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| formValue | - | array | 是 | - |
| formValue.fieldName | switchUser | string | 是 | - |
| formValue.fieldLabel | 选择员工 | string | 是 | 字段 |
| formValue.fieldValue | 1952551639793594400 | number | 是 | 字段 |
| formValue.fieldPerm | 1 | string | 是 | - |
| formValue.valueLables | - | null | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 审批调岗任务

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-06 15:59:20

> 更新时间: 2025-08-06 16:17:08

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/deptPostTask/taskApproveDeptPost

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "taskId":1953006571592114178,
    "taskStatus":1,  //1通过2驳回
    "formValue":[
        {
            "fieldName":"currentPostStatus",
            "fieldValue": 1947823414850945026
        },
        {
            "fieldName":"currentDeptState",
            "fieldValue": 1945307819278397441
        },
        {
            "fieldName":"adjustPostStatus",
            "fieldValue": 1944678525604741122
        },
        {
            "fieldName":"adjustDeptState",
            "fieldValue": 1945307819278397441
        }
    ]
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 转办审批任务

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-08 09:56:57

> 更新时间: 2025-08-08 16:19:50

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/task/transfer

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "taskId":1953385316517715970,
    "userId":1952551639793594370,  //转办用户id
    "remake":""//备注
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 消息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 15:24:42

> 更新时间: 2025-08-05 15:24:48

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 消息列表

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 15:29:12

> 更新时间: 2025-08-05 15:29:18

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/notice/getNoticeList?pageSize=15&pageNum=1

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageSize | 15 | string | 是 | - |
| pageNum | 1 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":2,"rows":[{"noticeId":"1952567739277131779","noticeTitle":"您有一条新的审批状态更新消息2","noticeType":"1","noticeContent":"whb,您提交的员工入职审批审批单已通过，请查阅","status":"0","remark":null,"createBy":"1952559738017325058","createByName":null,"createTime":"2025-08-05 15:10:01","relationId":"1952559738931683329","userId":"1952559738017325058","type":5},{"noticeId":"1952567739277131778","noticeTitle":"您有一条新的审批状态更新消息","noticeType":"1","noticeContent":"whb,您提交的员工入职审批审批单已通过，请查阅","status":"0","remark":null,"createBy":"1952559738017325058","createByName":null,"createTime":"2025-08-05 11:10:01","relationId":"1952559738931683329","userId":"1952559738017325058","type":5}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.noticeId | 1952567739277131779 | string | - |
| rows.noticeTitle | 您有一条新的审批状态更新消息2 | string | 消息标题 |
| rows.noticeType | 1 | string | 公告类型 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| rows.noticeContent | whb,您提交的员工入职审批审批单已通过，请查阅 | string | 消息内容 |
| rows.status | 0 | string | 公告状态（0未读 1已读） |
| rows.remark | - | null | - |
| rows.createBy | 1952559738017325058 | string | - |
| rows.createByName | - | null | 创建 |
| rows.createTime | 2025-08-05 15:10:01 | string | 创建时间 |
| rows.relationId | 1952559738931683329 | string | 关联id |
| rows.userId | 1952559738017325058 | string | - |
| rows.type | 5 | number | - |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 消息详情

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 15:35:00

> 更新时间: 2025-08-05 16:14:38

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/notice/getInfo?noticeId=1952567739277131779

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| noticeId | 1952567739277131779 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"noticeId":"1952567739277131779","noticeTitle":"您有一条新的审批状态更新消息2","noticeType":"1","noticeContent":"whb,您提交的员工入职审批审批单已通过，请查阅","status":"0","remark":null,"createBy":"1952559738017325058","createByName":null,"createTime":"2025-08-05 15:10:01","relationId":"1952559738931683329","userId":"1952559738017325058","type":5}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.noticeId | 1952567739277131779 | string | - |
| rows.noticeTitle | 您有一条新的审批状态更新消息2 | string | 消息标题 |
| rows.noticeType | 1 | string | 公告类型 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| rows.noticeContent | whb,您提交的员工入职审批审批单已通过，请查阅 | string | 消息内容 |
| rows.status | 0 | string | 公告状态（0未读 1已读） |
| rows.remark | - | null | - |
| rows.createBy | 1952559738017325058 | string | - |
| rows.createByName | - | null | 创建 |
| rows.createTime | 2025-08-05 15:10:01 | string | 创建时间 |
| rows.relationId | 1952559738931683329 | string | 关联id |
| rows.userId | 1952559738017325058 | string | - |
| rows.type | 5 | number | 5审批通过  3待审批 4抄送 6驳回 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 已读消息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 15:36:28

> 更新时间: 2025-08-05 15:36:28

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/notice/read?noticeId=1952567739277131779

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| noticeId | 1952567739277131779 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"noticeId":"1952567739277131779","noticeTitle":"您有一条新的审批状态更新消息2","noticeType":"1","noticeContent":"whb,您提交的员工入职审批审批单已通过，请查阅","status":"0","remark":null,"createBy":"1952559738017325058","createByName":null,"createTime":"2025-08-05 15:10:01","relationId":"1952559738931683329","userId":"1952559738017325058","type":5}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.noticeId | 1952567739277131779 | string | - |
| rows.noticeTitle | 您有一条新的审批状态更新消息2 | string | 消息标题 |
| rows.noticeType | 1 | string | 公告类型 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| rows.noticeContent | whb,您提交的员工入职审批审批单已通过，请查阅 | string | 消息内容 |
| rows.status | 0 | string | 公告状态（0未读 1已读） |
| rows.remark | - | null | - |
| rows.createBy | 1952559738017325058 | string | - |
| rows.createByName | - | null | 创建 |
| rows.createTime | 2025-08-05 15:10:01 | string | 创建时间 |
| rows.relationId | 1952559738931683329 | string | 关联id |
| rows.userId | 1952559738017325058 | string | - |
| rows.type | 5 | number | 5审批通过  3待审批 4抄送 6驳回 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 删除消息

> 创建人: 潘诺佩亚

> 更新人: 潘诺佩亚

> 创建时间: 2025-08-05 15:41:03

> 更新时间: 2025-08-05 15:41:03

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/notice/delete?noticeId=1952567739277131779

**请求方式**

> DELETE

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| noticeId | 1952567739277131779 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"noticeId":"1952567739277131779","noticeTitle":"您有一条新的审批状态更新消息2","noticeType":"1","noticeContent":"whb,您提交的员工入职审批审批单已通过，请查阅","status":"0","remark":null,"createBy":"1952559738017325058","createByName":null,"createTime":"2025-08-05 15:10:01","relationId":"1952559738931683329","userId":"1952559738017325058","type":5}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 2 | number | - |
| rows | - | array | - |
| rows.noticeId | 1952567739277131779 | string | - |
| rows.noticeTitle | 您有一条新的审批状态更新消息2 | string | 消息标题 |
| rows.noticeType | 1 | string | 公告类型 1入职审批单  2离职审批单 3调岗审批单 4补卡审批单 |
| rows.noticeContent | whb,您提交的员工入职审批审批单已通过，请查阅 | string | 消息内容 |
| rows.status | 0 | string | 公告状态（0未读 1已读） |
| rows.remark | - | null | - |
| rows.createBy | 1952559738017325058 | string | - |
| rows.createByName | - | null | 创建 |
| rows.createTime | 2025-08-05 15:10:01 | string | 创建时间 |
| rows.relationId | 1952559738931683329 | string | 关联id |
| rows.userId | 1952559738017325058 | string | - |
| rows.type | 5 | number | 5审批通过  3待审批 4抄送 6驳回 |
| code | 200 | number | - |
| msg | 查询成功 | string | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 普通上传

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-04 09:46:24

> 更新时间: 2025-08-04 10:21:37

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/upload

**请求方式**

> POST

**Content-Type**

> form-data

**请求Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| file | /Users/<USER>/Downloads/WX20240305-165139.png | file | 是 | 文件 |
| serviceType | 2 | string | 是 | 服务类型 1业务 2图片；3视频 4 音频 5 文档 6 zip |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "url": "http://*************:8080/xdc_local/image/2025/08/04/81199cde47c14a678f613ad99ec61a96.png",
        "fileName": "WX20240305-165139.png",
        "ossId": "1952193003615453185"
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.url | http://*************:8080/xdc_local/image/2025/08/04/81199cde47c14a678f613ad99ec61a96.png | string | 文件地址 |
| data.fileName | WX20240305-165139.png | string | 文件名 |
| data.ossId | 1952193003615453185 | string | 附件Id |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 分片上传

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-04 10:25:07

> 更新时间: 2025-08-04 10:25:07

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/upload_chunk

**请求方式**

> POST

**Content-Type**

> form-data

**请求Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| file | - | file | 是 | 文件 |
| serviceType | 2 | string | 是 | 服务类型 1业务 2图片；3视频 4 音频 5 文档 6 zip |
| fullSize | - | number | 是 | 文件大小 |
| fileName | - | string | 是 | 文件名 |
| chunks | - | string | 是 | 分片总数 |
| chunk | - | string | 是 | 当前分片数 |
| md5 | - | string | 是 | 文件md5 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 合并文件

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-04 10:52:42

> 更新时间: 2025-08-04 10:54:52

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/mergeFile

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"name": "武秀兰",
	"md5": "exercitation",
	"chunks": "culpa id",
	"serviceType": "nulla sunt aliqua culpa",
	"fullSize": "ut velit aliquip anim sunt"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | 武秀兰 | string | 否 | 文件名 |
| md5 | exercitation | string | 否 | 文件md5 |
| chunks | culpa id | string | 否 | 分片总数 |
| serviceType | nulla sunt aliqua culpa | string | 否 | 服务类型 1业务 2图片；3视频 4 音频 5 文档 6 zip |
| fullSize | ut velit aliquip anim sunt | string | 否 | 文件大小 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 验证文件

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-04 10:56:24

> 更新时间: 2025-08-04 10:58:28

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/common/verifyFile

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"name": "戴丽",
	"md5": "veniam mollit ea"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | 戴丽 | string | 否 | 文件名 |
| md5 | veniam mollit ea | string | 否 | 文件md5 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 清单任务

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-08-07 10:00:14

> 更新时间: 2025-08-07 10:00:14

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 选择清单列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-08-07 10:00:26

> 更新时间: 2025-08-07 10:00:26

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/inventoryTask/inventoryList?inventoryName=&cateId=&stepId=&stepCateId=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| inventoryName | - | string | 否 | 清单名称 |
| cateId | - | string | 否 | 清单分类id |
| stepId | - | string | 否 | 工序id |
| stepCateId | - | string | 否 | 工序分类id |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"inventoryId":"1952175749289066498","cateId":"1951219462925496322","stepId":"1951219463110045697","stepCateId":"1951219463051325441","cateName":"超前及地质不良作业线","inventoryName":"地质素描","stepName":"初支","stepCateName":"隧道施工"},{"inventoryId":"1952175749419089921","cateId":"1951219462925496322","stepId":"1951219463110045697","stepCateId":"1951219463051325441","cateName":"超前及地质不良作业线","inventoryName":"瞬变电磁法","stepName":"初支","stepCateName":"隧道施工"},{"inventoryId":"1952175749226151937","cateId":"1951219462925496322","stepId":"1951219463110045697","stepCateId":"1951219463051325441","cateName":"超前及地质不良作业线","inventoryName":"加深炮孔","stepName":"初支","stepCateName":"隧道施工"},{"inventoryId":"1952175749226151937","cateId":"1951219462925496322","stepId":"1951219463110045698","stepCateId":"1951219463051325441","cateName":"超前及地质不良作业线","inventoryName":"加深炮孔","stepName":"二衬","stepCateName":"隧道施工"},{"inventoryId":"1952175749289066498","cateId":"1951219462925496322","stepId":"1951219463110045697","stepCateId":"1953023017944686593","cateName":"超前及地质不良作业线","inventoryName":"地质素描","stepName":"初支","stepCateName":"桥梁工程"},{"inventoryId":"1952175749419089921","cateId":"1951219462925496322","stepId":"1951219463110045697","stepCateId":"1953023017944686593","cateName":"超前及地质不良作业线","inventoryName":"瞬变电磁法","stepName":"初支","stepCateName":"桥梁工程"},{"inventoryId":"1952175749226151937","cateId":"1951219462925496322","stepId":"1951219463110045697","stepCateId":"1953023017944686593","cateName":"超前及地质不良作业线","inventoryName":"加深炮孔","stepName":"初支","stepCateName":"桥梁工程"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.inventoryId | 1952175749289066498 | string | 清单id |
| data.cateId | 1951219462925496322 | string | 清单分类id |
| data.stepId | 1951219463110045697 | string | 工序id |
| data.stepCateId | 1951219463051325441 | string | 工序分类id |
| data.cateName | 超前及地质不良作业线 | string | 清单分类名称 |
| data.inventoryName | 地质素描 | string | 清单名称 |
| data.stepName | 初支 | string | 工序名称 |
| data.stepCateName | 隧道施工 | string | 工序分类名称 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工序分类列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-08-07 10:15:58

> 更新时间: 2025-08-07 10:15:58

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/inventoryTask/stepCateList

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepCateId":"1953023017944686593","cateName":"桥梁工程","createTime":"2025-08-06 17:19:08"},{"stepCateId":"1951219463051325441","cateName":"隧道施工","createTime":"2025-08-01 17:52:27"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.stepCateId | 1953023017944686593 | string | 工序分类id |
| data.cateName | 桥梁工程 | string | 工序分类名称 |
| data.createTime | 2025-08-06 17:19:08 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 工序列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-08-07 10:18:03

> 更新时间: 2025-08-07 10:18:03

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/inventoryTask/stepList?params[cateId]=1953023017944686593

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| params[cateId] | 1953023017944686593 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepId":"1951219463110045697","stepName":"初支","stepCode":"CCZZ","stepAlias":null,"stepSort":0,"cateIds":"1953023017944686593","cateNames":"桥梁工程","createTime":"2025-08-01 17:52:27"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.stepId | 1951219463110045697 | string | 工序id |
| data.stepName | 初支 | string | 工序名称 |
| data.stepCode | CCZZ | string | - |
| data.stepAlias | - | null | - |
| data.stepSort | 0 | number | - |
| data.cateIds | 1953023017944686593 | string | - |
| data.cateNames | 桥梁工程 | string | - |
| data.createTime | 2025-08-01 17:52:27 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 清单分类列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-08-07 10:18:59

> 更新时间: 2025-08-07 10:18:59

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/inventoryTask/inventoryCateList

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"id":"1951219462925496322","name":"超前及地质不良作业线","createTime":"2025-08-01 17:52:27"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.id | 1951219462925496322 | string | 清单分类id |
| data.name | 超前及地质不良作业线 | string | 清单分类名称 |
| data.createTime | 2025-08-01 17:52:27 | string | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 部门岗位用户列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-08-07 16:29:54

> 更新时间: 2025-08-08 17:22:32

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/inventoryTask/deptPostUserList?type=&parentId=&name=&deptId=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| type | - | string | 是 | 类型 0 无 1部门 2岗位 |
| parentId | - | string | 否 | 父id |
| name | - | string | 否 | 名称 |
| deptId | - | string | 是 | 部门id type为 2 必填 其他无需填 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"id":"1945656251901890561","name":"沈勇","type":"3","parentId":0,"isChild":"0"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.id | 1945656251901890561 | string | id |
| data.name | 沈勇 | string | 名称 |
| data.type | 3 | string | 类型  1部门 2岗位3用户 |
| data.parentId | 0 | number | 父id |
| data.isChild | 0 | string | 是否有下级0否1 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 员工考勤

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-07 16:24:32

> 更新时间: 2025-08-07 16:24:32

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 员工打卡

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-30 17:16:16

> 更新时间: 2025-08-08 14:48:50

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/attendance/checkIn

**请求方式**

> PUT

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"longitude": "112.551759",
	"latitude": "37.858121",
     "now":"2025-08-08 11:57:00"

}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| longitude | 112.551759 | string | 是 | 经度 有定位打卡 |
| latitude | 37.858121 | string | 是 | 纬度 有定位打卡 |
| pictures | - | string | 否 | 拍照开关 有拍照用 |
| address | - | string | 否 | 定位地址 有定位打卡 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 员工打卡界面

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-30 10:35:20

> 更新时间: 2025-08-08 18:04:27

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/attendance/checkInView

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"longitude": "112.551759",
	"latitude": "37.858121"
    //  "now":"2025-07-31 16:57:00"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| longitude | 112.551759 | string | 否 | 经度 |
| latitude | 37.858121 | string | 否 | 纬度 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "nickName": "沈勇",
        "projectName": "第一项目部",
        "deptPostStr": "测试-研发主管",
        "groupVo": {
            "id": "1949632906158776322",
            "name": "固定班",
            "blockType": "2,1",
            "shiftName": "测试班次1",
            "shiftTimes": [
                {
                    "id": "1948183636534677506",
                    "shiftId": "1948183636429819905",
                    "isSpan": "0",
                    "startTime": "08:31:00",
                    "checkInStartStatus": "未打卡",
                    "checkInStartTime": null,
                    "endTime": "12:00:00",
                    "checkInEndStatus": "未打卡",
                    "checkInEndTime": null,
                    "checkInStart": false,
                    "checkInEnd": false
                },
                {
                    "id": "1948183636547260418",
                    "shiftId": "1948183636429819905",
                    "isSpan": "0",
                    "startTime": "16:00:00",
                    "checkInStartStatus": "迟到",
                    "checkInStartTime": "2025-07-31 16:57:00",
                    "endTime": "18:00:00",
                    "checkInEndStatus": "正常",
                    "checkInEndTime": "2025-07-31 18:58:00",
                    "checkInStart": true,
                    "checkInEnd": true
                }
            ],
            "nowWorkDay": true,
            "isGoInLocation": true,
            "locationName": "山西大酒店",
            "buttonStatus": "1"
        }
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | object | 返回数据 |
| data.nickName | 沈勇 | string | 员工姓名 |
| data.projectName | 第一项目部 | string | 项目部名称 |
| data.deptPostStr | 测试-研发主管 | string | 员工部门岗位组 |
| data.groupVo | - | object | 考勤信息 |
| data.groupVo.id | 1949632906158776322 | string | 考勤 |
| data.groupVo.name | 固定班 | string | 考勤组名称 |
| data.groupVo.blockType | 2,1 | string | 打开方式 1GPS定位 2 拍照打卡 ，号分割 |
| data.groupVo.shiftName | 测试班次1 | string | 班次名称 |
| data.groupVo.shiftTimes | - | array | 考勤时间 |
| data.groupVo.shiftTimes.id | 1948183636534677506 | string | 时间段 |
| data.groupVo.shiftTimes.shiftId | 1948183636429819905 | string | - |
| data.groupVo.shiftTimes.isSpan | 0 | string | 是否跨日 0 否 1是 |
| data.groupVo.shiftTimes.startTime | 08:31:00 | string | 上班时间 |
| data.groupVo.shiftTimes.checkInStartStatus | 未打卡 | string | 上班卡状态 |
| data.groupVo.shiftTimes.checkInStartTime | - | null | 上班打卡时间 |
| data.groupVo.shiftTimes.endTime | 12:00:00 | string | 下班 |
| data.groupVo.shiftTimes.checkInEndStatus | 未打卡 | string | 下班卡状态 |
| data.groupVo.shiftTimes.checkInEndTime | - | null | 下班打卡 |
| data.groupVo.shiftTimes.checkInStart | false | boolean | 是否打上班卡 |
| data.groupVo.shiftTimes.checkInEnd | false | boolean | 是否打下班卡 |
| data.groupVo.nowWorkDay | true | boolean | 是否工作 false 为休息 |
| data.groupVo.isGoInLocation | true | boolean | 是否进入打卡范围 |
| data.groupVo.locationName | 山西大酒店 | string | 打卡地点 |
| data.groupVo.buttonStatus | 1 | string | 按钮状态 1上班2下班3不能 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 未命名接口

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-08-11 15:35:41

> 更新时间: 2025-08-11 15:35:41

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/front/attendance/checkRecord

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"YearMonth": "2025-08"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

## 大后台

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-11 09:27:09

> 更新时间: 2025-07-17 09:21:00

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | 客户端ID |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | 客户端ID |

**Query**

### 登录

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-11 11:35:17

> 更新时间: 2025-07-11 11:37:51

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /auth/login

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "tenantId": "000000",
    "username": "admin",
    "password": "admin123",
    "rememberMe": true,
    "uuid": "9f49385cd4384b05932c08127b2073f3",
    "code": "0",
    "clientId": "e5cd7e4891bf95d1d19206ce24a7b32e",
    "grantType": "password"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| tenantId | 000000 | string | 是 | 租户编号 |
| username | admin | string | 是 | 账号 |
| password | admin123 | string | 是 | 密码 |
| rememberMe | true | boolean | 是 | 记住我 |
| uuid | 9f49385cd4384b05932c08127b2073f3 | string | 是 | 图形验证码uuid |
| code | 0 | string | 是 | 验证码 |
| clientId | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | 客户端ID |
| grantType | password | string | 是 | 登录类型  password 密码 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 1,
    "msg": "",
    "data": {
        "access_token": "",
        "refresh_token": "",
        "expire_in": 1,
        "refresh_expire_in": 1,
        "client_id": "",
        "scope": "",
        "openid": ""
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 1 | integer | - |
| msg | - | string | - |
| data | - | object | - |
| data.access_token | - | string | 授权令牌 |
| data.refresh_token | - | string | 刷新令牌 |
| data.expire_in | 1 | integer | 授权令牌 access_token 的有效期 |
| data.refresh_expire_in | 1 | integer | 刷新令牌 refresh_token 的有效期 |
| data.client_id | - | string | 客户端id |
| data.scope | - | string | 令牌权限 |
| data.openid | - | string | 用户 openid |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 模版管理

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 11:56:20

> 更新时间: 2025-07-15 11:56:20

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 部门模版新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 11:34:15

> 更新时间: 2025-07-15 11:58:08

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/deptTemp

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "parentId": 1944598154489892866,
  "deptName": "测试1",
  "deptCategory": "",
  "orderNum": 0,
  "status":"0"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| parentId | 0 | number | 是 | 上级部门 |
| deptName | 测试 | string | 是 | 部门名称 |
| deptCategory | cs | string | 否 | 类别编号 |
| orderNum | 0 | number | 否 | 显示顺序 |
| status | 0 | string | 是 | 状态0正常1停用 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 部门模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 14:48:10

> 更新时间: 2025-07-21 11:36:37

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/deptTemp/list?deptName=&deptCategory=&status=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| deptName | - | string | 否 | 部门名称 |
| deptCategory | - | string | 否 | 类别编号 |
| status | - | string | 否 | 状态0正常1停用 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"deptId":"1944598154489892866","parentId":0,"ancestors":"0","deptName":"技术部","deptCategory":"cs","orderNum":0,"status":"0","createTime":"2025-07-14 11:21:44"},{"deptId":"1947130959505752066","parentId":0,"ancestors":"0","deptName":"122","deptCategory":null,"orderNum":0,"status":"0","createTime":"2025-07-21 11:06:11"},{"deptId":"1944667500893503489","parentId":"1944598154489892866","ancestors":"0,1944598154489892866","deptName":"工程管理","deptCategory":"","orderNum":0,"status":"0","createTime":"2025-07-14 15:57:17"},{"deptId":"1947129839064547329","parentId":"1944598154489892866","ancestors":"0,1944598154489892866","deptName":"测试1","deptCategory":null,"orderNum":0,"status":"0","createTime":"2025-07-21 11:01:44"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.deptId | 1944598154489892866 | string | - |
| data.parentId | 0 | number | 上级部门 |
| data.ancestors | 0 | string | 祖级列表 |
| data.deptName | 技术部 | string | 部门名称 |
| data.deptCategory | cs | string | 类别编号 |
| data.orderNum | 0 | number | 显示顺序 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.createTime | 2025-07-14 11:21:44 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 部门模版列表（排除节点）

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 14:58:07

> 更新时间: 2025-07-21 11:37:35

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/deptTemp/list/exclude/1944649243314634753

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"deptId":"1944598154489892866","parentId":0,"ancestors":"0","deptName":"技术部","deptCategory":"cs","orderNum":0,"status":"0","createTime":"2025-07-14 11:21:44"},{"deptId":"1947130959505752066","parentId":0,"ancestors":"0","deptName":"122","deptCategory":null,"orderNum":0,"status":"0","createTime":"2025-07-21 11:06:11"},{"deptId":"1944667500893503489","parentId":"1944598154489892866","ancestors":"0,1944598154489892866","deptName":"工程管理","deptCategory":"","orderNum":0,"status":"0","createTime":"2025-07-14 15:57:17"},{"deptId":"1947129839064547329","parentId":"1944598154489892866","ancestors":"0,1944598154489892866","deptName":"测试1","deptCategory":null,"orderNum":0,"status":"0","createTime":"2025-07-21 11:01:44"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.deptId | 1944598154489892866 | string | - |
| data.parentId | 0 | number | 上级部门 |
| data.ancestors | 0 | string | 祖级列表 |
| data.deptName | 技术部 | string | 部门名称 |
| data.deptCategory | cs | string | 类别编号 |
| data.orderNum | 0 | number | 显示顺序 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.createTime | 2025-07-14 11:21:44 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 部门模版树列表（搜索列表）

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-17 14:29:45

> 更新时间: 2025-07-17 14:30:06

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/deptTemp/deptTree

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"id":"1944598154489892866","parentId":0,"label":"测试","weight":0,"disabled":false,"children":[{"id":"1944667500893503489","parentId":"1944598154489892866","label":"测试1","weight":0,"disabled":false}]}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.id | 1944598154489892866 | string | 部门id |
| data.parentId | 0 | number | 上级部门 |
| data.label | 测试 | string | 名称 |
| data.weight | 0 | number | - |
| data.disabled | false | boolean | - |
| data.children | - | array | 子级 |
| data.children.id | 1944667500893503489 | string | - |
| data.children.parentId | 1944598154489892866 | string | - |
| data.children.label | 测试1 | string | - |
| data.children.weight | 0 | number | - |
| data.children.disabled | false | boolean | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取部门模版详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 14:59:58

> 更新时间: 2025-07-21 11:39:44

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/deptTemp/1944598154489892866

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"deptId":"1944598154489892866","parentId":0,"ancestors":"0","deptName":"技术部","deptCategory":"cs","orderNum":0,"status":"0","createTime":"2025-07-14 11:21:44"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.deptId | 1944598154489892866 | string | 部门 |
| data.parentId | 0 | number | 上级部门 |
| data.ancestors | 0 | string | 祖级列表 |
| data.deptName | 技术部 | string | 部门名称 |
| data.deptCategory | cs | string | 类别编号 |
| data.orderNum | 0 | number | 显示顺序 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.createTime | 2025-07-14 11:21:44 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改部门模版

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 15:58:31

> 更新时间: 2025-07-15 11:58:31

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/deptTemp

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "deptId": "1944649243314634753",
  "parentId": 0,
  "deptName": "3333",
  "deptCategory": null,
  "orderNum": 0,
  "status": "0"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| parentId | 0 | number | 是 | 上级部门 |
| deptName | 测试 | string | 是 | 部门名称 |
| deptCategory | cs | string | 否 | 类别编号 |
| orderNum | 0 | number | 否 | 显示顺序 |
| status | 0 | string | 是 | 状态0正常1停用 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 部门模版删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 16:09:05

> 更新时间: 2025-07-22 16:12:06

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/deptTemp/1944649243314634753

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 岗位模版新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 16:33:03

> 更新时间: 2025-07-22 17:42:19

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/postTemp

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "postName": "测试岗位1",
  "postCode": "",
  "postSort": 0,
  "status":"0",
  "remark":""
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| postName | 测试岗位1 | string | 是 | 岗位名称 |
| postCode | - | string | 是 | 岗位编码 |
| postSort | 0 | number | 是 | 显示顺序 |
| status | 0 | string | 是 | 状态0正常1停用 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 岗位模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 16:41:36

> 更新时间: 2025-07-22 17:42:43

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/postTemp/list?postName=&postCode=&status=&pageNum=1&pageSize=10

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| postName | - | string | 否 | 岗位名称 |
| postCode | - | string | 否 | 岗位编号 |
| status | - | string | 否 | 状态0正常1停用 |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":1,"rows":[{"postId":"1944678525604741122","postCode":"","postCategory":"","postName":"技术员","postSort":0,"status":"0","createTime":"2025-07-14 16:41:06"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.postId | 1944678525604741122 | string | 岗位id |
| rows.postCode | - | string | 岗位编号 |
| rows.postName | 技术员 | string | 岗位名称 |
| rows.postSort | 0 | number | 显示顺序 |
| rows.status | 0 | string | 状态0正常1停用 |
| rows.createTime | 2025-07-14 16:41:06 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 岗位模版列表（搜索列表）

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-17 14:43:45

> 更新时间: 2025-07-22 17:42:57

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/postTemp/listAll?postName=&postCode=&status=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| postName | - | string | 否 | 岗位名称 |
| postCode | - | string | 否 | 岗位编号 |
| status | - | string | 否 | 状态0正常1停用 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"postId":"1944678525604741122","postCode":"","postCategory":"","postName":"技术员","postSort":0,"status":"0","createTime":"2025-07-14 16:41:06"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.postId | 1944678525604741122 | string | 岗位id |
| data.postCode | - | string | 岗位编号 |
| data.postCategory | - | string | 类别编号 |
| data.postName | 技术员 | string | 岗位名称 |
| data.postSort | 0 | number | 显示顺序 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.createTime | 2025-07-14 16:41:06 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取岗位模版详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 16:45:25

> 更新时间: 2025-07-22 17:43:20

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/postTemp/1944678525604741122

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"postId":"1944678525604741122","postCode":"","postCategory":"","postName":"技术员","postSort":0,"status":"0","createTime":"2025-07-14 16:41:06"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.postId | 1944678525604741122 | string | - |
| data.postCode | - | string | 岗位编号 |
| data.postName | 技术员 | string | 岗位名称 |
| data.postSort | 0 | number | 显示顺序 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.createTime | 2025-07-14 16:41:06 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改岗位模版

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 16:48:03

> 更新时间: 2025-07-22 17:43:40

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/postTemp

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "postId": "1944676444990930946",
  "postCode": "",
  "postName": "岗位1",
  "postSort": 0,
  "status": "0",
  "remark":""
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| postId | 1944676444990930946 | string | 是 | 岗位id |
| postCode | - | string | 是 | 岗位编号 |
| postName | 岗位1 | string | 是 | 岗位名称 |
| postSort | 0 | number | 是 | 显示顺序 |
| status | 0 | string | 是 | 状态0正常1停用 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 岗位模版删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 16:49:40

> 更新时间: 2025-07-22 18:01:06

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/postTemp/1944676444990930946

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 组模版新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-14 18:05:50

> 更新时间: 2025-07-15 11:58:59

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/groupTemp

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "groupName": "测试组2",
  "groupSort": 0,
  "remark":""
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | 测试组 | string | 是 | 组名称 |
| groupSort | 0 | number | 否 | 显示顺序 |
| remark | - | string | 否 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 组模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 11:47:33

> 更新时间: 2025-07-21 11:42:15

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/groupTemp/list?groupName=&pageNum=1&pageSize=10

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | - | string | 否 | 组名称 |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":1,"rows":[{"groupId":"1944968716408115201","groupName":"现场组","groupSort":0,"remark":"","createTime":"2025-07-15 11:54:12"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.groupId | 1944968716408115201 | string | 组id |
| rows.groupName | 现场组 | string | 组名称 |
| rows.groupSort | 0 | number | 显示顺序 |
| rows.remark | - | string | 备注 |
| rows.createTime | 2025-07-15 11:54:12 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 组模版列表（搜索列表）

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-17 14:45:14

> 更新时间: 2025-07-21 11:42:34

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/groupTemp/listAll?groupName=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupName | - | string | 否 | 组名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"groupId":"1944968716408115201","groupName":"现场组","groupSort":0,"remark":"","createTime":"2025-07-15 11:54:12"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.groupId | 1944968716408115201 | string | 组id |
| data.groupName | 现场组 | string | 组名称 |
| data.groupSort | 0 | number | 显示顺序 |
| data.remark | - | string | 备注 |
| data.createTime | 2025-07-15 11:54:12 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取组模版详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 11:49:40

> 更新时间: 2025-07-21 11:43:03

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/groupTemp/1944968716408115201

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"groupId":"1944968716408115201","groupName":"现场组","groupSort":0,"remark":"","createTime":"2025-07-15 11:54:12"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.groupId | 1944968716408115201 | string | 组id |
| data.groupName | 现场组 | string | 组名称 |
| data.groupSort | 0 | number | 显示顺序 |
| data.remark | - | string | 备注 |
| data.createTime | 2025-07-15 11:54:12 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改组模版

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 11:52:03

> 更新时间: 2025-07-15 11:59:14

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/groupTemp

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "groupId": "1944963660237529090",
  "groupName": "测试组1",
  "groupSort": 0,
  "remark":""
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 组模版删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 11:55:01

> 更新时间: 2025-07-22 18:01:00

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/groupTemp/1944963660237529090

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序分类模版新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 14:28:42

> 更新时间: 2025-07-15 14:46:44

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCateTemp

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "cateName": "路基施工"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | 测试组2 | string | 是 | 工序分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序分类模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 14:30:17

> 更新时间: 2025-07-21 11:43:32

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCateTemp/list?cateName=&pageNum=1&pageSize=10

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | - | string | 否 | 工序分类名称 |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":3,"rows":[{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"},{"stepCateId":"1945011846935228417","cateName":"隧道施工","createTime":"2025-07-15 14:45:36"},{"stepCateId":"1945011560883695618","cateName":"桥梁施工","createTime":"2025-07-15 14:44:27"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 3 | number | - |
| rows | - | array | - |
| rows.stepCateId | 1945011893089349633 | string | 分类 |
| rows.cateName | 路基施工 | string | 工序分类名称 |
| rows.createTime | 2025-07-15 14:45:47 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序分类模版列表(搜索列表)

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 08:31:06

> 更新时间: 2025-07-25 08:31:22

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCateTemp/listAll?cateName=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientId | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | - | string | 否 | 工序分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"},{"stepCateId":"1945011846935228417","cateName":"隧道施工","createTime":"2025-07-15 14:45:36"},{"stepCateId":"1945011560883695618","cateName":"桥梁施工","createTime":"2025-07-15 14:44:27"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.stepCateId | 1945011893089349633 | string | 工序分类id |
| data.cateName | 路基施工 | string | 工序分类名称 |
| data.createTime | 2025-07-15 14:45:47 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientId | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取工序分类模版详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 08:33:15

> 更新时间: 2025-07-25 08:35:11

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCateTemp/1945011893089349633

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.stepCateId | 1945011893089349633 | string | 工序分类id |
| data.cateName | 路基施工 | string | 工序分类名称 |
| data.createTime | 2025-07-15 14:45:47 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改工序模版分类

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 08:34:14

> 更新时间: 2025-07-25 08:35:17

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCateTemp

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "stepCateId": "1945011893089349633",
  "cateName": "路基施工"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序分类模版删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 08:34:41

> 更新时间: 2025-07-25 08:35:19

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workCate/1947949747534778370

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序模版新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 16:29:38

> 更新时间: 2025-07-15 16:29:38

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStepTemp

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "stepName": "洞身开挖",
  "stepCode":"",
  "stepAlias":"",
  "stepBefore":1945037202924871682,
  "stepSort":0,
  "cateIds":[1945011846935228417]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| stepName | 路基施工 | string | 是 | 工序名称 |
| stepCode | - | string | 否 | 工序编号 |
| stepAlias | - | string | 否 | 工序别名 |
| stepBefore | - | string | 否 | 前置工序 |
| stepSort | 0 | number | 否 | 显示顺序 |
| cateIds | - | array | 否 | 所属分类 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-15 18:02:45

> 更新时间: 2025-07-23 15:23:41

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStepTemp/list?pageNum=1&pageSize=10&stepName=&stepCode=&stepAlias=&params[cateId]=&stepBefore=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |
| stepName | - | string | 否 | 工序名称 |
| stepCode | - | string | 否 | 工序编号 |
| stepAlias | - | string | 否 | 工序别名 |
| params[cateId] | - | string | 否 | 所属分类 |
| stepBefore | - | string | 否 | 前置工序 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":3,"rows":[{"stepId":"1947819580269375489","stepName":"二衬","stepCode":null,"stepAlias":null,"stepBefore":"1947819350790615042","stepBeforeName":"仰拱","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:42:31"},{"stepId":"1947819350790615042","stepName":"仰拱","stepCode":null,"stepAlias":null,"stepBefore":"1947819213892726786","stepBeforeName":"初支","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:37"},{"stepId":"1947819213892726786","stepName":"初支","stepCode":null,"stepAlias":null,"stepBefore":0,"stepBeforeName":null,"stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:04"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 3 | number | - |
| rows | - | array | - |
| rows.stepId | 1947819580269375489 | string | - |
| rows.stepName | 二衬 | string | 工序名称 |
| rows.stepCode | - | null | 工序编号 |
| rows.stepAlias | - | null | 工序别名 |
| rows.stepBefore | 1947819350790615042 | string | 前置工序 |
| rows.stepBeforeName | 仰拱 | string | 前置工序 |
| rows.stepSort | 0 | number | 显示顺序 |
| rows.cateIds | 1945011846935228417 | string | 分类 |
| rows.cateNames | 隧道施工 | string | 所属分类 |
| rows.createTime | 2025-07-23 08:42:31 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序模版列表(搜索列表)

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-16 09:53:07

> 更新时间: 2025-07-23 15:26:57

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStepTemp/listAll?stepName=&stepCode=&stepAlias=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| stepName | - | string | 否 | 工序名称 |
| stepCode | - | string | 否 | 工序编号 |
| stepAlias | - | string | 否 | 工序别名 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepId":"1947819580269375489","stepName":"二衬","stepCode":null,"stepAlias":null,"stepBefore":"1947819350790615042","stepBeforeName":"仰拱","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:42:31"},{"stepId":"1947819350790615042","stepName":"仰拱","stepCode":null,"stepAlias":null,"stepBefore":"1947819213892726786","stepBeforeName":"初支","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:37"},{"stepId":"1947819213892726786","stepName":"初支","stepCode":null,"stepAlias":null,"stepBefore":0,"stepBeforeName":null,"stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:41:04"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.stepId | 1947819580269375489 | string | - |
| data.stepName | 二衬 | string | 工序名称 |
| data.stepCode | - | null | 工序编号 |
| data.stepAlias | - | null | 工序别名 |
| data.stepBefore | 1947819350790615042 | string | 前置工序 |
| data.stepBeforeName | 仰拱 | string | 前置工序 |
| data.stepSort | 0 | number | - |
| data.cateIds | 1945011846935228417 | string | - |
| data.cateNames | 隧道施工 | string | 所属分类名称 |
| data.createTime | 2025-07-23 08:42:31 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取工序模版详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-16 09:58:56

> 更新时间: 2025-07-23 15:27:56

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStepTemp/1947819580269375489

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"stepId":"1947819580269375489","stepName":"二衬","stepCode":null,"stepAlias":null,"stepBefore":"1947819350790615042","stepBeforeName":"仰拱","stepSort":0,"cateIds":"1945011846935228417","cateNames":"隧道施工","createTime":"2025-07-23 08:42:31"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.stepId | 1947819580269375489 | string | - |
| data.stepName | 二衬 | string | 工序名称 |
| data.stepCode | - | null | 工序编号 |
| data.stepAlias | - | null | 工序别名 |
| data.stepBefore | 1947819350790615042 | string | 前置工序 |
| data.stepBeforeName | 仰拱 | string | 前置工序 |
| data.stepSort | 0 | number | 显示顺序 |
| data.cateIds | 1945011846935228417 | string | 分类ids |
| data.cateNames | 隧道施工 | string | 所属分类名称 |
| data.createTime | 2025-07-23 08:42:31 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改工序模版

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-16 10:00:11

> 更新时间: 2025-07-16 10:00:11

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStepTemp

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "stepId": "1945037841478295553",
  "stepName": "洞身开挖",
  "stepCode":"",
  "stepAlias":"",
  "stepBefore":1945037202924871682,
  "stepSort":0,
  "cateIds":[1945011846935228417]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 工序模版删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-16 10:03:23

> 更新时间: 2025-07-22 18:00:54

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/workStepTemp/1945037841478295553

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单模版新增

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-16 11:50:32

> 更新时间: 2025-07-22 15:19:21

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "inventoryName": "超前水平钻孔99",
  "cateId":1947230213406466049,
  "beforeInventory":0,
  "remark":"",
  "status":"0",
  "stepList":[
    {
      "stepId":1945037202924871682,
      "sort":0
    }
  ],
  "relationList":[
    {
      "assignType":"1",
      "relationId":1944667500893503489
    },
    {
      "assignType":"1",
      "relationId":1944598154489892866
    },
    {
      "assignType":"2",
      "relationId":1944678525604741122
    }
  ],
  "groupList":[
    {
      "groupId":"1944968716408115201",
      "score":5,
      "formInfo":
      {
        "rulesJson":[{"type":"stepper","field":"Fmoamd723ppjaec","title":"搭接进度","info":"","$required":false,"_fc_id":"id_Fpylmd723ppjafc","name":"ref_Fohkmd723ppjagc","display":true,"hidden":false,"_fc_drag_tag":"stepper","value":1},{"type":"switch","field":"Fxcpmd724lg8ahc","title":"孔数是否符合设计","info":"","$required":false,"props":{"activeValue":true,"inactiveValue":false},"_fc_id":"id_F4ugmd724lg8aic","name":"ref_Fr01md724lg8ajc","display":true,"hidden":false,"_fc_drag_tag":"switch"}],
        "optionsJson":{"language":{"zh-cn":{"Az87OmQS":"商品名称","BAVvUidu":"商品价格","CkD1fG2H":"商品描述","DgH2iJ3K":"库存数量","EhI3jK4L":"发货方式","FiJ4kL5M":"配送时间","GjK5lM6N":"用户评价","HkL6mN7O":"添加到购物车","IkM7nO8P":"立即购买","JlN8oP9Q":"优惠活动","KmO9pQ0R":"搜索商品","LnP0qR1S":"分类","MoQ1rS2T":"品牌","NpR2sT3U":"付款方式","OqS3tU4V":"订单确认","PrT4uV5W":"用户注册","QsU5vW6X":"用户登录","RtV6wX7Y":"联系客服","SuW7xY8Z":"退出登录","TvX8yZ9A":"个人信息","UwY9zA0B":"购物车","VxZ0aB1C":"结算","WyA1bC2D":"运费","XzB2cD3E":"订单状态","YaC3dE4F":"支付成功","ZbD4eF5G":"支付失败"},"en":{"Az87OmQS":"Goods name","BAVvUidu":"Goods price","CkD1fG2H":"Product description","DgH2iJ3K":"Stock quantity","EhI3jK4L":"Shipping method","FiJ4kL5M":"Delivery time","GjK5lM6N":"User reviews","HkL6mN7O":"Add to cart","IkM7nO8P":"Buy now","JlN8oP9Q":"Promotions","KmO9pQ0R":"Search products","LnP0qR1S":"Category","MoQ1rS2T":"Brand","NpR2sT3U":"Payment method","OqS3tU4V":"Order confirmation","PrT4uV5W":"User registration","QsU5vW6X":"User login","RtV6wX7Y":"Contact customer service","SuW7xY8Z":"Logout","TvX8yZ9A":"Personal information","UwY9zA0B":"Shopping cart","VxZ0aB1C":"Checkout","WyA1bC2D":"Shipping fee","XzB2cD3E":"Order status","YaC3dE4F":"Payment successful","ZbD4eF5G":"Payment failed"}},"form":{"labelWidth":"6.2em","colon":false},"resetBtn":{"show":true,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"超前水平钻孔"}
      }
    }
  ]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| inventoryName | 超前水平钻孔99 | string | 是 | 清单名称 |
| cateId | 1947230213406466000 | number | 是 | 所属分类 |
| beforeInventory | 0 | number | 是 | 前置清单 |
| remark | - | string | 是 | 备注 |
| status | 0 | string | 是 | 状态 0 草稿 1正常 2停用 |
| stepList | - | array | 是 | 所属工序 |
| stepList.stepId | 1945037202924871700 | number | 是 | 工序id |
| stepList.sort | 0 | number | 是 | 顺序 |
| relationList | - | array | 是 | 权限关联 |
| relationList.assignType | 1 | string | 是 | 分配类型 1部门分配 2 岗位分配 |
| relationList.relationId | 1944667500893503500 | number | 是 | 关联id |
| groupList | - | array | 是 | 所属组 |
| groupList.groupId | 1944968716408115201 | string | 是 | 组id |
| groupList.score | 5 | number | 是 | 分数 |
| groupList.formInfo | - | object | 是 | 表单 |
| groupList.formInfo.rulesJson | - | array | 是 | 表单的规则和字段的整体配置数据 |
| groupList.formInfo.rulesJson.type | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.field | Fmoamd723ppjaec | string | 是 | - |
| groupList.formInfo.rulesJson.title | 搭接进度 | string | 是 | - |
| groupList.formInfo.rulesJson.info | - | string | 是 | - |
| groupList.formInfo.rulesJson.$required | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_id | id_Fpylmd723ppjafc | string | 是 | - |
| groupList.formInfo.rulesJson.name | ref_Fohkmd723ppjagc | string | 是 | - |
| groupList.formInfo.rulesJson.display | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.hidden | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_drag_tag | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.value | 1 | number | 是 | - |
| groupList.formInfo.rulesJson.props | - | object | 是 | - |
| groupList.formInfo.rulesJson.props.activeValue | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.props.inactiveValue | false | boolean | 是 | - |
| groupList.formInfo.optionsJson | - | object | 是 | 表单的配置数据 |
| groupList.formInfo.optionsJson.language | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.Az87OmQS | 商品名称 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.BAVvUidu | 商品价格 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.CkD1fG2H | 商品描述 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.DgH2iJ3K | 库存数量 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.EhI3jK4L | 发货方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.FiJ4kL5M | 配送时间 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.GjK5lM6N | 用户评价 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.HkL6mN7O | 添加到购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.IkM7nO8P | 立即购买 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.JlN8oP9Q | 优惠活动 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.KmO9pQ0R | 搜索商品 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.LnP0qR1S | 分类 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.MoQ1rS2T | 品牌 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.NpR2sT3U | 付款方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.OqS3tU4V | 订单确认 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.PrT4uV5W | 用户注册 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.QsU5vW6X | 用户登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.RtV6wX7Y | 联系客服 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.SuW7xY8Z | 退出登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.TvX8yZ9A | 个人信息 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.UwY9zA0B | 购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.VxZ0aB1C | 结算 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.WyA1bC2D | 运费 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.XzB2cD3E | 订单状态 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.YaC3dE4F | 支付成功 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.ZbD4eF5G | 支付失败 | string | 是 | - |
| groupList.formInfo.optionsJson.language.en | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.en.Az87OmQS | Goods name | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.BAVvUidu | Goods price | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.CkD1fG2H | Product description | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.DgH2iJ3K | Stock quantity | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.EhI3jK4L | Shipping method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.FiJ4kL5M | Delivery time | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.GjK5lM6N | User reviews | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.HkL6mN7O | Add to cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.IkM7nO8P | Buy now | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.JlN8oP9Q | Promotions | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.KmO9pQ0R | Search products | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.LnP0qR1S | Category | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.MoQ1rS2T | Brand | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.NpR2sT3U | Payment method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.OqS3tU4V | Order confirmation | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.PrT4uV5W | User registration | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.QsU5vW6X | User login | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.RtV6wX7Y | Contact customer service | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.SuW7xY8Z | Logout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.TvX8yZ9A | Personal information | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.UwY9zA0B | Shopping cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.VxZ0aB1C | Checkout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.WyA1bC2D | Shipping fee | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.XzB2cD3E | Order status | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.YaC3dE4F | Payment successful | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.ZbD4eF5G | Payment failed | string | 是 | - |
| groupList.formInfo.optionsJson.form | - | object | 是 | - |
| groupList.formInfo.optionsJson.form.labelWidth | 6.2em | string | 是 | - |
| groupList.formInfo.optionsJson.form.colon | false | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.innerText | 重置 | string | 是 | - |
| groupList.formInfo.optionsJson.submitBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.innerText | 提交 | string | 是 | - |
| groupList.formInfo.optionsJson.formName | 超前水平钻孔 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-18 14:27:40

> 更新时间: 2025-07-22 15:20:28

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp/list?pageNum=1&pageSize=100000&inventoryName=&beforeInventory=&status=&params[stepId]=&params[groupId]=&params[deptId]=&params[postId]=&cateId=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 100000 | string | 是 | 条数 |
| inventoryName | - | string | 否 | 清单名称 |
| beforeInventory | - | string | 否 | 前置清单 |
| status | - | string | 否 | 状态 0 草稿 1正常 2停用 |
| params[stepId] | - | string | 否 | 所属工序 |
| params[groupId] | - | string | 否 | 所属组 |
| params[deptId] | - | string | 否 | 所属部门 |
| params[postId] | - | string | 否 | 所属岗位 |
| cateId | - | string | 否 | 所属分类 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":8,"rows":[{"inventoryId":"1947554121854836738","cateId":0,"cateName":"","inventoryName":"超前水平钻孔999","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-22 15:07:41"},{"inventoryId":"1947457252814315521","cateId":"1947230213406466049","cateName":"超前及地质不良作业线","inventoryName":"超前水平钻孔99","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-22 08:42:46"},{"inventoryId":"1946056066395553793","cateId":0,"cateName":"","inventoryName":"超前水平钻孔6","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:54:57"},{"inventoryId":"1946055074157441025","cateId":0,"cateName":"","inventoryName":"超前水平钻孔4","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:51:00"},{"inventoryId":"1946050391804334082","cateId":0,"cateName":"","inventoryName":"超前水平钻孔3","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-18 11:32:24"},{"inventoryId":"1945775059629109250","cateId":0,"cateName":"","inventoryName":"超前水平钻孔2","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 17:18:20"},{"inventoryId":"1945770211009490946","cateId":0,"cateName":"","inventoryName":"超前水平钻孔1","beforeInventory":"1945759589827887105","beforeInventoryName":"超前水平钻孔","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 16:59:04"},{"inventoryId":"1945759589827887105","cateId":0,"cateName":"","inventoryName":"超前水平钻孔","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"工程管理","postNames":"技术员","groupNames":"现场组","groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-17 16:16:51"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 8 | number | - |
| rows | - | array | - |
| rows.inventoryId | 1947554121854836738 | string | 清单id |
| rows.cateId | 0 | number | 所属分类 |
| rows.cateName | - | string | 所属分类名称 |
| rows.inventoryName | 超前水平钻孔999 | string | 清单名称 |
| rows.beforeInventory | 0 | number | 前置清单 |
| rows.beforeInventoryName | - | string | 前置清单名称 |
| rows.remark | - | string | 备注 |
| rows.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| rows.stepNames | 超前及地质不良作业线 | string | 所属工序 |
| rows.deptNames | 技术部,工程管理 | string | 所属部门 |
| rows.postNames | 技术员 | string | 所属 |
| rows.groupNames | 现场组 | string | 所属 |
| rows.groupList | - | null | - |
| rows.stepList | - | null | - |
| rows.relationList | - | null | - |
| rows.createTime | 2025-07-22 15:07:41 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取清单模版信息（编辑回显）

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-18 14:26:58

> 更新时间: 2025-07-23 16:12:25

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp/1947822446988038146

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"inventoryId":"1947457252814315521","cateId":"1947230213406466049","cateName":null,"inventoryName":"超前水平钻孔99","beforeInventory":0,"beforeInventoryName":null,"remark":"","status":"0","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":[{"inventoryId":"1947457252814315521","groupId":"1944968716408115201","score":5,"formInfo":{"formId":"1947457253804171265","inventoryId":"1947457252814315521","groupId":"1944968716408115201","title":"超前水平钻孔","description":null,"rulesJson":[],"optionsJson":{}}}],"stepList":[{"inventoryId":"1947457252814315521","stepId":"1945037202924871682","sort":0}],"relationList":[{"id":"1947457253451849730","inventoryId":"1947457252814315521","assignType":"1","relationId":"1944598154489892866"},{"id":"1947457253426683906","inventoryId":"1947457252814315521","assignType":"1","relationId":"1944667500893503489"},{"id":"1947457253460238337","inventoryId":"1947457252814315521","assignType":"2","relationId":"1944678525604741122"}],"createTime":"2025-07-22 08:42:46"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.inventoryId | 1947457252814315521 | string | 清单id |
| data.cateId | 1947230213406466049 | string | 所属分类 |
| data.cateName | - | null | - |
| data.inventoryName | 超前水平钻孔99 | string | 清单名称 |
| data.beforeInventory | 0 | number | 前置 |
| data.beforeInventoryName | - | null | 前置清单名称 |
| data.remark | - | string | 备注 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.stepNames | - | null | - |
| data.deptNames | - | null | - |
| data.postNames | - | null | - |
| data.groupNames | - | null | - |
| data.groupList | - | array | 所属 |
| data.groupList.inventoryId | 1947457252814315521 | string | 清单id |
| data.groupList.groupId | 1944968716408115201 | string | 组id |
| data.groupList.score | 5 | number | 分数 |
| data.groupList.formInfo | - | object | 表单相关 |
| data.groupList.formInfo.formId | 1947457253804171265 | string | 表单id |
| data.groupList.formInfo.inventoryId | 1947457252814315521 | string | 清单id |
| data.groupList.formInfo.groupId | 1944968716408115201 | string | 组id |
| data.groupList.formInfo.title | 超前水平钻孔 | string | 表单 |
| data.groupList.formInfo.description | - | null | 表单描述 |
| data.groupList.formInfo.rulesJson | - | array | 表单的规则和字段的整体配置数据，通常包含多个字段的配置 |
| data.groupList.formInfo.optionsJson | - | object | 表单的配置数据（例如：布局、尺寸、全局数据等） |
| data.stepList | - | array | 所属工序 |
| data.stepList.inventoryId | 1947457252814315521 | string | 清单id |
| data.stepList.stepId | 1945037202924871682 | string | 工序id |
| data.stepList.sort | 0 | number | 清单显示 |
| data.relationList | - | array | 权限配置 |
| data.relationList.id | 1947457253451849730 | string |  |
| data.relationList.inventoryId | 1947457252814315521 | string | 清单id |
| data.relationList.assignType | 1 | string | 分配类型 1部门分配 2 岗位分配 |
| data.relationList.relationId | 1944598154489892866 | string | 关联id |
| data.createTime | 2025-07-22 08:42:46 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改清单模版

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 10:45:01

> 更新时间: 2025-07-22 15:23:27

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "inventoryId":1946056066395553793,
  "inventoryName": "超前水平钻孔6",
  "cateId":1947230213406466049,
  "beforeInventory":0,
  "remark":"",
  "status":"0",
  "stepList":[
    {
      "stepId":1945037202924871682,
      "sort":0
    }
  ],
  "relationList":[
    {
      "assignType":"1",
      "relationId":1944667500893503489
    },
    {
      "assignType":"1",
      "relationId":1944598154489892866
    },
    {
      "assignType":"2",
      "relationId":1944678525604741122
    }
  ],
  "groupList":[
    {
      "groupId":"1944968716408115201",
      "score":5,
      "formInfo":
      {
        "rulesJson":[{"type":"stepper","field":"Fmoamd723ppjaec","title":"搭接进度","info":"","$required":false,"_fc_id":"id_Fpylmd723ppjafc","name":"ref_Fohkmd723ppjagc","display":true,"hidden":false,"_fc_drag_tag":"stepper","value":1},{"type":"switch","field":"Fxcpmd724lg8ahc","title":"孔数是否符合设计","info":"","$required":false,"props":{"activeValue":true,"inactiveValue":false},"_fc_id":"id_F4ugmd724lg8aic","name":"ref_Fr01md724lg8ajc","display":true,"hidden":false,"_fc_drag_tag":"switch"}],
        "optionsJson":{"language":{"zh-cn":{"Az87OmQS":"商品名称","BAVvUidu":"商品价格","CkD1fG2H":"商品描述","DgH2iJ3K":"库存数量","EhI3jK4L":"发货方式","FiJ4kL5M":"配送时间","GjK5lM6N":"用户评价","HkL6mN7O":"添加到购物车","IkM7nO8P":"立即购买","JlN8oP9Q":"优惠活动","KmO9pQ0R":"搜索商品","LnP0qR1S":"分类","MoQ1rS2T":"品牌","NpR2sT3U":"付款方式","OqS3tU4V":"订单确认","PrT4uV5W":"用户注册","QsU5vW6X":"用户登录","RtV6wX7Y":"联系客服","SuW7xY8Z":"退出登录","TvX8yZ9A":"个人信息","UwY9zA0B":"购物车","VxZ0aB1C":"结算","WyA1bC2D":"运费","XzB2cD3E":"订单状态","YaC3dE4F":"支付成功","ZbD4eF5G":"支付失败"},"en":{"Az87OmQS":"Goods name","BAVvUidu":"Goods price","CkD1fG2H":"Product description","DgH2iJ3K":"Stock quantity","EhI3jK4L":"Shipping method","FiJ4kL5M":"Delivery time","GjK5lM6N":"User reviews","HkL6mN7O":"Add to cart","IkM7nO8P":"Buy now","JlN8oP9Q":"Promotions","KmO9pQ0R":"Search products","LnP0qR1S":"Category","MoQ1rS2T":"Brand","NpR2sT3U":"Payment method","OqS3tU4V":"Order confirmation","PrT4uV5W":"User registration","QsU5vW6X":"User login","RtV6wX7Y":"Contact customer service","SuW7xY8Z":"Logout","TvX8yZ9A":"Personal information","UwY9zA0B":"Shopping cart","VxZ0aB1C":"Checkout","WyA1bC2D":"Shipping fee","XzB2cD3E":"Order status","YaC3dE4F":"Payment successful","ZbD4eF5G":"Payment failed"}},"form":{"labelWidth":"6.2em","colon":false},"resetBtn":{"show":true,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"formName":"超前水平钻孔"}
      }
    }
  ]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| inventoryId | 1946056066395553800 | number | 是 | 清单id |
| inventoryName | 超前水平钻孔6 | string | 是 | 清单名称 |
| cateId | 1947230213406466000 | number | 是 | 所属分类 |
| beforeInventory | 0 | number | 是 | 前置 |
| remark | - | string | 是 | 备注 |
| status | 0 | string | 是 | 状态 0 草稿 1正常 2停用 |
| stepList | - | array | 是 | 所属工序 |
| stepList.stepId | 1945037202924871700 | number | 是 | 工序id |
| stepList.sort | 0 | number | 是 | 清单显示 |
| relationList | - | array | 是 | 权限配置 |
| relationList.assignType | 1 | string | 是 | 分配类型 1部门分配 2 岗位分配 |
| relationList.relationId | 1944667500893503500 | number | 是 | 关联id |
| groupList | - | array | 是 | 所属 |
| groupList.groupId | 1944968716408115201 | string | 是 | 组id |
| groupList.score | 5 | number | 是 | 分数 |
| groupList.formInfo | - | object | 是 | 表单相关 |
| groupList.formInfo.rulesJson | - | array | 是 | 表单的规则和字段的整体配置数据，通常包含多个字段的配置 |
| groupList.formInfo.rulesJson.type | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.field | Fmoamd723ppjaec | string | 是 | - |
| groupList.formInfo.rulesJson.title | 搭接进度 | string | 是 | - |
| groupList.formInfo.rulesJson.info | - | string | 是 | - |
| groupList.formInfo.rulesJson.$required | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_id | id_Fpylmd723ppjafc | string | 是 | - |
| groupList.formInfo.rulesJson.name | ref_Fohkmd723ppjagc | string | 是 | - |
| groupList.formInfo.rulesJson.display | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.hidden | false | boolean | 是 | - |
| groupList.formInfo.rulesJson._fc_drag_tag | stepper | string | 是 | - |
| groupList.formInfo.rulesJson.value | 1 | number | 是 | - |
| groupList.formInfo.rulesJson.props | - | object | 是 | - |
| groupList.formInfo.rulesJson.props.activeValue | true | boolean | 是 | - |
| groupList.formInfo.rulesJson.props.inactiveValue | false | boolean | 是 | - |
| groupList.formInfo.optionsJson | - | object | 是 | 表单的配置数据（例如：布局、尺寸、全局数据等） |
| groupList.formInfo.optionsJson.language | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.Az87OmQS | 商品名称 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.BAVvUidu | 商品价格 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.CkD1fG2H | 商品描述 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.DgH2iJ3K | 库存数量 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.EhI3jK4L | 发货方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.FiJ4kL5M | 配送时间 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.GjK5lM6N | 用户评价 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.HkL6mN7O | 添加到购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.IkM7nO8P | 立即购买 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.JlN8oP9Q | 优惠活动 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.KmO9pQ0R | 搜索商品 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.LnP0qR1S | 分类 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.MoQ1rS2T | 品牌 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.NpR2sT3U | 付款方式 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.OqS3tU4V | 订单确认 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.PrT4uV5W | 用户注册 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.QsU5vW6X | 用户登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.RtV6wX7Y | 联系客服 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.SuW7xY8Z | 退出登录 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.TvX8yZ9A | 个人信息 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.UwY9zA0B | 购物车 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.VxZ0aB1C | 结算 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.WyA1bC2D | 运费 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.XzB2cD3E | 订单状态 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.YaC3dE4F | 支付成功 | string | 是 | - |
| groupList.formInfo.optionsJson.language.zh-cn.ZbD4eF5G | 支付失败 | string | 是 | - |
| groupList.formInfo.optionsJson.language.en | - | object | 是 | - |
| groupList.formInfo.optionsJson.language.en.Az87OmQS | Goods name | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.BAVvUidu | Goods price | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.CkD1fG2H | Product description | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.DgH2iJ3K | Stock quantity | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.EhI3jK4L | Shipping method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.FiJ4kL5M | Delivery time | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.GjK5lM6N | User reviews | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.HkL6mN7O | Add to cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.IkM7nO8P | Buy now | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.JlN8oP9Q | Promotions | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.KmO9pQ0R | Search products | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.LnP0qR1S | Category | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.MoQ1rS2T | Brand | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.NpR2sT3U | Payment method | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.OqS3tU4V | Order confirmation | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.PrT4uV5W | User registration | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.QsU5vW6X | User login | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.RtV6wX7Y | Contact customer service | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.SuW7xY8Z | Logout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.TvX8yZ9A | Personal information | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.UwY9zA0B | Shopping cart | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.VxZ0aB1C | Checkout | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.WyA1bC2D | Shipping fee | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.XzB2cD3E | Order status | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.YaC3dE4F | Payment successful | string | 是 | - |
| groupList.formInfo.optionsJson.language.en.ZbD4eF5G | Payment failed | string | 是 | - |
| groupList.formInfo.optionsJson.form | - | object | 是 | - |
| groupList.formInfo.optionsJson.form.labelWidth | 6.2em | string | 是 | - |
| groupList.formInfo.optionsJson.form.colon | false | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.resetBtn.innerText | 重置 | string | 是 | - |
| groupList.formInfo.optionsJson.submitBtn | - | object | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.show | true | boolean | 是 | - |
| groupList.formInfo.optionsJson.submitBtn.innerText | 提交 | string | 是 | - |
| groupList.formInfo.optionsJson.formName | 超前水平钻孔 | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取清单模版详情

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 17:13:07

> 更新时间: 2025-07-22 15:34:05

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp/detail/1946056066395553793

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"inventoryId":"1946056066395553793","cateId":"1947230213406466049","cateName":"超前及地质不良作业线","inventoryName":"超前水平钻孔6","beforeInventory":0,"beforeInventoryName":"","remark":"","status":"0","stepNames":"超前及地质不良作业线","deptNames":"技术部,工程管理","postNames":"技术员","groupNames":"现场组","groupList":[{"inventoryId":"1946056066395553793","groupId":"1944968716408115201","groupName":"现场组","score":5,"formInfo":{"formId":"1947557864268750850","inventoryId":"1946056066395553793","groupId":"1944968716408115201","title":"超前水平钻孔","description":null,"rulesJson":[],"optionsJson":{}}}],"stepList":null,"relationList":null,"createTime":"2025-07-18 11:54:57"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.inventoryId | 1946056066395553793 | string | 清单id |
| data.cateId | 1947230213406466049 | string | 所属分类 |
| data.cateName | 超前及地质不良作业线 | string | 分类名称 |
| data.inventoryName | 超前水平钻孔6 | string | 清单名称 |
| data.beforeInventory | 0 | number | 前置 |
| data.beforeInventoryName | - | string | 前置清单名称 |
| data.remark | - | string | 备注 |
| data.status | 0 | string | 状态 0 草稿 1正常 2停用 |
| data.stepNames | 超前及地质不良作业线 | string | 所属 |
| data.deptNames | 技术部,工程管理 | string | 关联部门 |
| data.postNames | 技术员 | string | 关联岗位 |
| data.groupNames | 现场组 | string | 关联组 |
| data.groupList | - | array | 所属组 |
| data.groupList.inventoryId | 1946056066395553793 | string | 清单id |
| data.groupList.groupId | 1944968716408115201 | string | 组id |
| data.groupList.groupName | 现场组 | string | - |
| data.groupList.score | 5 | number | 分数 |
| data.groupList.formInfo | - | object | 表单相关 |
| data.groupList.formInfo.formId | 1947557864268750850 | string | 表单id |
| data.groupList.formInfo.inventoryId | 1946056066395553793 | string | 清单id |
| data.groupList.formInfo.groupId | 1944968716408115201 | string | 组id |
| data.groupList.formInfo.title | 超前水平钻孔 | string | 表单 |
| data.groupList.formInfo.description | - | null | 表单描述 |
| data.groupList.formInfo.rulesJson | - | array | 表单的规则和字段的整体配置数据，通常包含多个字段的配置 |
| data.groupList.formInfo.optionsJson | - | object | 表单的配置数据（例如：布局、尺寸、全局数据等） |
| data.stepList | - | null | 所属工序 |
| data.relationList | - | null | 权限配置 |
| data.createTime | 2025-07-18 11:54:57 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单模版列表(搜索列表) 

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-23 15:42:50

> 更新时间: 2025-07-25 11:34:42

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp/listAll?inventoryName=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| inventoryName | - | string | 否 | 清单名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"inventoryId":"1947822446988038146","cateId":"1947819719310553089","cateName":null,"inventoryName":"超前水平钻孔","beforeInventory":0,"beforeInventoryName":null,"remark":"","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 08:53:55"},{"inventoryId":"1947856995080040449","cateId":"1947820129584787457","cateName":null,"inventoryName":"测试清单111","beforeInventory":"1947822446988038146","beforeInventoryName":null,"remark":"123","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 11:11:12"},{"inventoryId":"1947867289080655873","cateId":"1947820203786219522","cateName":null,"inventoryName":"书法大赛得分","beforeInventory":"1947856995080040449","beforeInventoryName":null,"remark":"123123","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 11:52:06"},{"inventoryId":"1947903030414372865","cateId":"1947820235595821058","cateName":null,"inventoryName":"111","beforeInventory":0,"beforeInventoryName":null,"remark":"","status":"1","stepNames":null,"deptNames":null,"postNames":null,"groupNames":null,"groupList":null,"stepList":null,"relationList":null,"createTime":"2025-07-23 14:14:07"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.inventoryId | 1947822446988038146 | string | - |
| data.cateId | 1947819719310553089 | string | - |
| data.cateName | - | null | - |
| data.inventoryName | 超前水平钻孔 | string | 清单名称 |
| data.beforeInventory | 0 | number | - |
| data.beforeInventoryName | - | null | - |
| data.remark | - | string | - |
| data.status | 1 | string | - |
| data.stepNames | - | null | - |
| data.deptNames | - | null | - |
| data.postNames | - | null | - |
| data.groupNames | - | null | - |
| data.groupList | - | null | - |
| data.stepList | - | null | - |
| data.relationList | - | null | - |
| data.createTime | 2025-07-23 08:53:55 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单模版删除 

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-25 11:34:27

> 更新时间: 2025-07-25 11:34:46

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryTemp/1947224540304121858

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类模版新增 

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 17:18:07

> 更新时间: 2025-07-21 17:18:07

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCateTemp

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "name": "超前及地质不良作业线"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| cateName | 测试组2 | string | 是 | 工序分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类模版列表

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 17:22:45

> 更新时间: 2025-07-21 17:22:55

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCateTemp/list?name=&pageNum=1&pageSize=10

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | - | string | 否 | 清单分类名称 |
| pageNum | 1 | string | 是 | 页数 |
| pageSize | 10 | string | 是 | 条数 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"total":1,"rows":[{"id":"1947224540304121858","name":"超前及地质不良作业线","createTime":"2025-07-21 17:18:03"}],"code":200,"msg":"查询成功"}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| total | 1 | number | - |
| rows | - | array | - |
| rows.id | 1947224540304121858 | string | - |
| rows.name | 超前及地质不良作业线 | string | 清单分类名称 |
| rows.createTime | 2025-07-21 17:18:03 | string | 创建时间 |
| code | 200 | number | - |
| msg | 查询成功 | string | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类模版列表(搜索列表)

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 17:24:39

> 更新时间: 2025-07-21 17:24:39

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCateTemp/listAll?name=

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| name | - | string | 否 | 清单分类名称 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":[{"stepCateId":"1945011893089349633","cateName":"路基施工","createTime":"2025-07-15 14:45:47"},{"stepCateId":"1945011846935228417","cateName":"隧道施工","createTime":"2025-07-15 14:45:36"},{"stepCateId":"1945011560883695618","cateName":"桥梁施工","createTime":"2025-07-15 14:44:27"}]}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | array | - |
| data.stepCateId | 1945011893089349633 | string | 工序分类id |
| data.cateName | 路基施工 | string | 工序分类名称 |
| data.createTime | 2025-07-15 14:45:47 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 获取清单分类模版详细信息

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 17:25:40

> 更新时间: 2025-07-21 17:25:40

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCateTemp/1947224540304121858

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":{"id":"1947224540304121858","name":"超前及地质不良作业线","createTime":"2025-07-21 17:18:03"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | object | - |
| data.id | 1947224540304121858 | string | - |
| data.name | 超前及地质不良作业线 | string | 清单分类名称 |
| data.createTime | 2025-07-21 17:18:03 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 修改清单分类模版

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 17:26:56

> 更新时间: 2025-07-21 17:26:56

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCateTemp

**请求方式**

> PUT

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**请求Body参数**

```javascript
{
  "id": "1947224540304121858",
  "name": "超前及地质不良作业线"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| groupId | 1944963660237529090 | string | 是 | 组id |
| groupName | 测试组1 | string | 是 | 组名称 |
| groupSort | 0 | number | 是 | 显示顺序 |
| remark | - | string | 是 | 备注 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

#### 清单分类模版删除

> 创建人: #H zx.

> 更新人: #H zx.

> 创建时间: 2025-07-21 17:27:48

> 更新时间: 2025-07-22 18:01:14

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> /system/inventoryCateTemp/1947224540304121858

**请求方式**

> DELETE

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{"code":200,"msg":"操作成功","data":null}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | - |
| data | - | null | - |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | - |

**Query**

### 租户管理

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 09:04:42

> 更新时间: 2025-07-16 09:04:42

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 开通租户

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-16 09:46:44

> 更新时间: 2025-07-16 11:00:44

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/tenant

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"projectName": "第一项目部1",
	"contactUserName": "冯鑫",
	"contactPhone": "18636984056",
	"username": "fengxin",
	"password": "xdc@xdc1",
	"address": "南湖路422号",
	"intro": "aliquip adipisicing exercitation",
	"beginTime": "1973-10-22 22:22:07",
	"endTime": "2002-04-02 23:27:52",
	"unit": "officia id"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| projectName | 第一项目部 | string | 是 | 项目名的 |
| contactUserName | 冯鑫 | string | 是 | 施工复杂人 |
| contactPhone | 18636984056 | string | 是 | 联系电话 |
| username | 冯鑫 | string | 是 | 管理员账号 |
| password | xdc@xdc1 | string | 是 | 管理员密码 |
| address | 南湖路422号 | string | 否 | 工程地点 |
| intro | aliquip adipisicing exercitation | string | 否 | 项目简介 |
| beginTime | 1973-10-22 22:22:07 | string | 否 | 开工时间 |
| endTime | 2002-04-02 23:27:52 | string | 否 | 竣工时间 |
| unit | officia id | string | 否 | 施工单位 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 同步字典

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 09:22:28

> 更新时间: 2025-07-17 09:23:31

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/tenant/syncTenantDict

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

## 字典接口

> 创建人: 蛋仔星星

> 更新人: 蛋仔星星

> 创建时间: 2025-07-17 09:32:40

> 更新时间: 2025-07-17 09:32:40

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system/dict/data/type/{dictType}

**请求方式**

> GET

**Content-Type**

> none

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | 客户端ID |

**路径变量**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| dictType | sys_education | string | 是 | 字典类型 |

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "dictCode": "1945655439997882370",
            "dictSort": 0,
            "dictLabel": "专科",
            "dictValue": "1",
            "dictType": "sys_education",
            "cssClass": "",
            "listClass": "default",
            "isDefault": "N",
            "remark": "",
            "createTime": "2025-07-17 09:23:00"
        },
        {
            "dictCode": "1945655439997882371",
            "dictSort": 1,
            "dictLabel": "本科",
            "dictValue": "2",
            "dictType": "sys_education",
            "cssClass": "",
            "listClass": "default",
            "isDefault": "N",
            "remark": "",
            "createTime": "2025-07-17 09:23:00"
        },
        {
            "dictCode": "1945655439997882372",
            "dictSort": 2,
            "dictLabel": "研究生",
            "dictValue": "3",
            "dictType": "sys_education",
            "cssClass": "",
            "listClass": "default",
            "isDefault": "N",
            "remark": "",
            "createTime": "2025-07-17 09:23:00"
        },
        {
            "dictCode": "1945655439997882373",
            "dictSort": 3,
            "dictLabel": "其他",
            "dictValue": "4",
            "dictType": "sys_education",
            "cssClass": "",
            "listClass": "default",
            "isDefault": "N",
            "remark": "",
            "createTime": "2025-07-17 09:23:00"
        }
    ]
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | - |
| msg | 操作成功 | string | 返回文字描述 |
| data | - | array | 返回数据 |
| data.dictCode | 1945655439997882370 | string | - |
| data.dictSort | 0 | number | - |
| data.dictLabel | 专科 | string | 字典标识 |
| data.dictValue | 1 | string | 字典 |
| data.dictType | sys_education | string | 字典类型 |
| data.cssClass | - | string | - |
| data.listClass | default | string | - |
| data.isDefault | N | string | - |
| data.remark | - | string | 备注 |
| data.createTime | 2025-07-17 09:23:00 | string | 创建时间 |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientid | e5cd7e4891bf95d1d19206ce24a7b32e | string | 是 | 客户端ID |

**Query**
