<template>
	<view class="status_bar">
		<!-- 这里是状态栏 -->
	</view>
	<view class="page-container">
		<view class="nav-bar">
		    
		    <view class="nav-btn" @tap="handleBack">
		      <text class="iconfont icon_font">&#xe696;</text>
		    </view>
		    <text class="title">{{title}}</text>
		
		    <view class="nav-btn" @click="handleSearch" >
				<text class="iconfont icon_font" >&#xe7e7;</text>
		    </view>
		</view>
		<view class="" style="flex:1;">
			<scrollRefresh :isRefreshing="refreshing"
			  :isLoading="loading"
			  :hasMore="hasMore"
			  @refresh="onRefreshTriggered"
			  @loadMore="onLoadMoreTriggered">
			   <view class="content-list" >
				   <view class="zb_item" @click="handleDetails(item)" v-for="(item,index) in listData" :key="index">
					   <view class="zb_item_label">
							<text class="zb_name">{{item.nickName}}</text>
							<text class="zb_gw">
								{{item.gw}}
							</text>
					   							
					   </view>
					   <view class="zk_item_content">
							<text class="zk_item_label">考核名称：</text>
							<text class="zk_item_title">{{item.name}}</text>
					   </view>
					   <view class="zk_item_content">
							<text class="zk_item_label">考核得分：</text>
							<view class="khdf">
								<text class="font_28 color_13">系统打分{{item.xtdf}}*主管系数分 </text>
								<text class="font_28" style="color: #0BAB00;">{{item.zgxfs}}</text> 
								<text class="font_28 color_13" >={{item.score}}</text>
							</view>
					   </view>
					   <view class="zk_item_content">
							<text class="zk_item_label">考核排名：</text>
							<text class="zk_item_title">{{item.khpm}}</text>
					   </view>
				   </view>
			   </view>
			</scrollRefresh>
			
		</view>
		
		
		<view class="mask_view" v-if="popupShow==true">
			<view class="popup_content">
				<!-- <text class="popup_header">搜索</text> -->
				
				<view class="popup_content">
					<view class="search">
						<input
							class="input_view"
							v-model="searchVal"
							style="width: 100%;"
							placeholder="请输入清单名称进行搜索" 
							placeholder-class="placeholder"
						/>
					</view>
					
				</view>
				
				<view class="button-group">
				  <button class="btn cancel" @click="closeModal">取消</button>
				  <button class="btn confirm" @click="handleConfirm">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {pageType} from '@/utils/apiType.uts'
	import scrollRefresh from '@/components/scroll-refresh.uvue'
	
	// 定义数据结构
	type ListItem = {
	  id: number
	  name?: string,
	  time?: string,
	  nickName?:string,
	  score?: string|number,
	  gw?:string,
	  title?:string,
	  xtdf?:string|number,
	  zgxfs?:1,
	  khpm?:6
	}
	// 标题名称
	const title=ref<string>('周考评分记录')
	
	const refreshing = ref(false)
	const loading = ref(false)
	const hasMore = ref(true)
	const listData = ref<ListItem[]>([]) // 您的数据
	// 分页
	const pageInfo=ref<pageType>({
		page:1,
		pageSize:15,
		total:0
	})
	
	// 搜索弹窗
	const popupShow=ref<boolean>(false)
	// 搜索条件
	const searchVal=ref<string>('')
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	
	
	
	
	// 获取数据函数
	const fetchData = () => {
	
		if (loading.value || !hasMore.value) return
		loading.value=true
		setTimeout(() => {
			const newData: ListItem[] = []
			for (let i = 0; i < pageInfo.value.pageSize; i++) {
				 const id = (pageInfo.value.page - 1) * pageInfo.value.pageSize+ i + 1
				 newData.push({
				   id: id,
				   name:'2025年2月13日清单',
				   nickName:'张某某',
				   gw:'土建工程师',
				   khpm:6,
				   zgxfs:1,
				   xtdf:100,
				   score:100
				 })
			}
			listData.value = pageInfo.value.page === 1 
				 ? newData 
				 : [...listData.value, ...newData]
			
			// 数据结束条件（可调整或移除）
			if(listData.value.length>=pageInfo.value.total){
				hasMore.value = false
			}
			// if (pageInfo.value.page >= 5) hasMore.value = false
			
			loading.value = false
			refreshing.value = false
			pageInfo.value.page++
			
		}, 800)
	  
	}
	
	// 下拉刷新事件
	const onRefreshTriggered = () => {
	  if (refreshing.value) return
	  
	  refreshing.value = true
	  pageInfo.value.page = 1
	  hasMore.value = true
	  pageInfo.value.pageSize = 15 // 重置为初始值
	  
	  setTimeout(() => {
	    fetchData()
	  }, 1000)
	}
	
	// 上拉加载更多事件
	const onLoadMoreTriggered = () => {
		 if (!hasMore.value || loading.value) return
		 
		 // 每次加载固定数量 (无需修改pageSize)
		 // pageInfo.value.pageSize = 15
		 fetchData()
	}
	
	
	// 取消弹窗
	const closeModal=()=>{
		console.log('取消');
		popupShow.value=false
	}
	
	// 搜索
	const handleSearch=()=>{
		console.log('搜索');
		if(popupShow.value==true){
			pageInfo.value.page = 1
			fetchData()
			popupShow.value=false
		}else{
			popupShow.value=true
			
		}
	}
		
	// 弹窗确认
	const handleConfirm=()=>{
		console.log('弹窗确认');
		 pageInfo.value.page = 1
		 fetchData()
		popupShow.value=false
	}
	
	
	// 跳转详情
	const handleDetails=(item:ListItem)=>{
		console.log('跳转详情');
		uni.navigateTo({
			url:'/pages/mine/pingfenjilu/weekRateDetails?id='+item.id
		})
	}
	
	// 页面加载时初始化数据
	onLoad((options: OnLoadOptions) => {
		
		fetchData()
	})
	
</script>

<style lang="scss" scoped>
.page-container{
	width: 100%;
	height: 100%;
}
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  // height: 88px;
  // margin-top: 88rpx;
  padding: 30rpx 0rpx;
  background-color: #FFFFFF; 
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */
  // position: fixed;
  // top: 0;
  // left: 0;
  // width: 100%;
  // z-index: 99999;
}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #000000; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #000000; /* 黑色文字 */
}

/* 内容列表 */
.content-list {
  padding: 20rpx;
  background: #f3f5f8;
  .zb_item{
	  background: #FFFFFF;
	  padding: 30rpx 30rpx;
	  box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
	  margin-bottom: 20rpx;
	  .zb_item_label{
	  	display: flex;
	  	flex-direction: row;
	  	flex-wrap: wrap;
	  	align-items: center;
	  	overflow: visible;
		
	  	
	  }
	  .zb_name{
	  	color: #131313;
	  	font-size: 28rpx;
	  	font-weight: 700;
	  }
	  .zb_gw{
	  	font-size: 27rpx;
	  	padding: 5rpx 10rpx;
	  	color: #FFB300;
	  	background: #FFF6D6;
	  	margin-left: 5rpx;
	  }
	  
	  .zk_item_content{
	  	display: flex;
	  	flex-direction: row;
	  	
	  	margin-top: 20rpx;
	  	
	  }
	  .zk_item_label{
	  	color: #8B8B8B;
	  	font-size: 28rpx;
	  }
	  .zk_item_title{
	  	color: #131313;
	  	font-size: 28rpx;
		
	  }
	  .khdf{
		display: flex;
		flex-direction: row;
	  }
  }
}
.font_28{
	font-size: 27rpx;
}
.color_13{
	color: #131313;
}
.mask_view{
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	justify-content: flex-end;
	.popup_content{
		width: 100%;
		height: 90%;
		// padding: 20rpx 30rpx;
		background: #FFFFFF;
		margin-top: 44px;
		
		
		
	}
	.search{
		width:90%;
		margin:0 auto;
	}
	.input_view{
		background: #F7F8FA;
		border-radius: 35rpx 35rpx 35rpx 35rpx;
		border: 1rpx solid #ECEDF1;
		padding: 10px 30rpx;
	}
	.popup_header{
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		font-weight: 400;
		color: #000;
		margin-bottom: 40rpx;
		padding: 20rpx 30rpx;
		height: 6%;
		
		
	}
	.button-group {
	  display: flex;
	  flex-direction: row;
	  justify-content: space-around;
	  border-radius: 20rpx;
	  // height: 6%;
	  .btn{
		  padding: 0 60rpx;
		  font-size: 30rpx;
		  border-radius: 40rpx;
		  
	  }
	  .cancel{
		  background: #EFEFEF;
	  }
	  .confirm{
		  background: #0189F6;
		  color: #fff;
	  }
	  
	}
	.popup_content{
		height: 80%;
	}
}
</style>
