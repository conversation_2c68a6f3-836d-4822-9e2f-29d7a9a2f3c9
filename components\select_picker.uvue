<template>
	<view class="mask_picker"  v-if="show">
		<view class="picker_content">
			<view class="picker_header">
				<button class="quxiao" @click="handlePickerClose">
					取消
				</button>
				<button class="queren" @click="handlePickerConfirm">
					确认
				</button>
			</view>
			<picker-view 
			  class="picker" 
			  :value="option" @change="bindChange">
			  <picker-view-column >
				<view 
				  class="item" 
				  v-for="(item,index) in items" 
				  :key="index">
				  {{item.label}}
				</view>
			  </picker-view-column>
			</picker-view>
		</view>
	</view>
</template>

<script setup>
	import {ref, PropType} from 'vue'

	import {ProjectOption,confirmHcType} from '@/utils/apiType.uts'
	import { login } from '../common/api'
	
	type etype={
		detail:detailsType
	}
	type detailsType={
		value:number,
		// laber:string
	}

	const props = defineProps({
	  show: { // 控制弹窗显示
	    type: Boolean,
	    default: false
	  },
	  items: { // 控制弹窗显示
	    type: Array as PropType<ProjectOption[]>,
	    default: () =>[]
	  },
	  option:{
		  type:Array as PropType<number[]>,
		  default:0
	  }
	});
	const emit = defineEmits(['close', 'confirm']);
	
	const selectedIndex=ref<number[]>([0])
	
	
	// 选择事件
	const handlePickerConfirm=()=>{
		
		const selectedItem = props.items[selectedIndex.value[0]]
		const params:confirmHcType={
			selectedItem:selectedItem,index:selectedIndex.value[0]
		}
		emit('confirm',params)
	}
	// 关闭
	const handlePickerClose=()=>{
		emit('close')
	}
	
	const bindChange=(e : UniPickerViewChangeEvent)=> {
		// console.log(e);
		selectedIndex.value=e.detail.value
	}
	
	onLoad(()=>{
		selectedIndex.value[0]=props.option[0]
	})
</script>

<style lang="scss" scoped>
.mask_picker{
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
}
.picker_content{
	
	width:100%;
	height: 30%;
	background: #fff;
	position: absolute;
	bottom: 0;
}
.picker {
  height: 100%;
  margin: 20rpx 0;
}
.item {
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.picker_header{
	
	position: relative;
	text-align: center;
	width: 100%;
	padding: 20rpx 40rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	font-size: 30rpx;
	
}
.quxiao{
	color: #888;
}
.queren{
	color: #007aff;
}

</style>